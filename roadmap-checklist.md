# Roadmap e Checklist de Implementação - PostoAki Infra

Este documento apresenta um roadmap e checklist para implementação da infraestrutura do PostoAki, organizando os componentes e tarefas necessárias para completar a migração.

## Checklist de Implementação

### 1. Serviços de Aplicação

| Serviço | Status | Observações | Prioridade |
|---------|--------|-------------|------------|
| ⬜ Metabase | Pendente | Falta banco de dados (Postgres) | Alta |
| ⬜ Serviço Preço | Pendente | Falta banco de dados (Postgres) | Alta |
| ✅ Site | Não iniciado | Tem dependência de banco de dados (MySQL) | Média |
| ✅ Wiki (wiki.postoaki.com.br) | Pendente | Repositório: https://dev.azure.com/postoaki/PostoAki/_git/postoaki-requisitos-wiki | Média |
| ✅ Redirect (redirect.postoaki.com.br) | Pendente | Repositório: https://dev.azure.com/postoaki/PostoAki/_git/postoaki-redirect | Média |

### 2. Servidores e Infraestrutura

| Servidor | Status | Componentes | Prioridade |
|----------|--------|-------------|------------|
| ⬜ Servidor de Preço | Pendente | Pipeline Azure configurado: https://dev.azure.com/postoaki/PostoAki/_build?definitionId=122 | Alta |
| ✅ Servidor Painel | Pendente | | Alta |
| ✅ Servidor Owncloud (Drive) | Pendente | drive2.postoaki.com.br, Armazenamento 4TB | Alta |
| ⬜ Servidor de Homologação | Pendente | Múltiplos domínios e serviços | Média |

### 3. Bancos de Dados

| Decisão | Status | Observações | Prioridade |
|---------|--------|-------------|------------|
| ⬜ Definir estratégia de banco de dados | Pendente | Opções:<br>- 1 servidor com todos os bancos (Kubernetes ou VM)<br>- Usar servidor BDados (Produção) | Alta |

### 4. Backup e Armazenamento

| Componente | Status | Detalhes | Prioridade |
|------------|--------|----------|------------|
| ⬜ Configuração de backup Metabase | Pendente | Frequência: semanal | Alta |
| ⬜ Configuração de backup Tomcat Homologação | Pendente | Frequência: semanal | Média |
| ⬜ Implementação política LGPD para backups | Pendente | Política de retenção definida | Alta |

### 5. Usuários e Permissões

| Tipo de Usuário | Status | Finalidade | Prioridade |
|-----------------|--------|------------|------------|
| ⬜ postoaki | Pendente | Armazena logs, banco de desenvolvimento | Alta |
| ⬜ backupprod | Pendente | Armazena o backup do banco de produção | Alta |
| ⬜ treinamento | Pendente | Vídeos da plataforma utilizados no Wiki | Média |

## Política de Retenção de Backup (LGPD)

- ⬜ Backup diário às 22h15 - 2h15
- ⬜ A cada 7 dias: excluir backups completos realizados às 2h15
- ⬜ A cada 3 meses: manter apenas um backup por semana
- ⬜ A cada 1 ano: manter apenas um backup por mês
- ⬜ A cada 3 anos: manter apenas um backup por trimestre
- ⬜ A cada 5 anos: exclusão total dos backups

## Containers Atuais a Migrar

### Servidor Principal

- ✅ docker-nginx (ee90b774f688)
- ✅ owncloud/server:latest (cac0442ca4fd)
- ✅ redis:6 (ce2bc8c2df0b)
- ✅ mariadb:10 (4e39852afd0d)

### Servidor de Homologação

- ✅ tomcat:9-jdk21-temurin (8d8588590229)
- ✅ portainer/portainer-ce:latest (c2ce5a0310cd)
- ✅ docker-nginx (7796c6b3cee8)
- ✅ edoburu/pgbouncer:latest (e67585bfa3a8)
- ✅ tomcat:8.5-jdk17 (c9e7da2d8da1)
- ✅ tomcat:8.5-jdk17 (4629456eeacf)
- ✅ postgres:16-alpine (078024c0b924)
- ⬜ sonarqube:latest (4391ecb424a0)
- ⬜ postgres:13-alpine (997c9639232c)

## Scripts de Automação

### Script de Servidor Preço

```bash
#!/bin/bash

nomeDockerTomcat=tomcat-servico-preco
nomeBancoPostgres=postoakiservico
nomeDockerPostgres=postgres

echo 'Pausando ambiente'
docker stop $nomeDockerTomcat

echo 'Reiniciando Postgres'
docker restart $nomeDockerPostgres

echo 'Aguardar 10 segundos'
sleep 10

echo 'Parando todas as sessões do banco de dados'
docker exec -it $nomeDockerPostgres psql -U postgres -d postgres -c "SELECT pg_cancel_backend(pid) FROM pg_stat_activity where datname = '$nomeBancoPostgres' order by 1 desc";

echo 'Excluindo banco de dados'
docker exec -it $nomeDockerPostgres psql -U postgres -d postgres -c "DROP DATABASE IF EXISTS $nomeBancoPostgres";

echo 'Criando novo banco de dados'
docker exec -it $nomeDockerPostgres psql -U postgres -d postgres -c "CREATE DATABASE $nomeBancoPostgres";

echo 'Startando ambiente'
docker start $nomeDockerTomcat
```

### Sincronização de Empresas

```bash
curl --location 'http://apiservicopreco.postoaki.com.br/api/v1/suporte/sync'
```

## Próximos Passos

1. Priorizar a implementação dos serviços de banco de dados (Postgres) para Metabase e Serviço Preço
2. Configurar o Servidor Owncloud com armazenamento de 4TB
3. Implementar a política de backup e retenção conforme LGPD
4. Migrar os containers existentes para a nova infraestrutura
5. Configurar os domínios e redirecionamentos necessários

## Domínios a Configurar para Homologação

- gameficacaoweb.postoaki.com.br
- painelgameficacao.postoaki.com.br
- painelhomolog.postoaki.com.br
- painelmaster.postoaki.com.br
- painelvendamaster.postoaki.com.br
- portainer.postoaki.com.br
- sonarqube.postoaki.com.br
- apigameficacao.postoaki.com.br
- apihomolog.postoaki.com.br
- apimaster.postoaki.com.br