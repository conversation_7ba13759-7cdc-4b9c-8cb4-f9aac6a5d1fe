apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: blackbox-exporter
  namespace: monitoring
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: prometheus-blackbox-exporter
  endpoints:
    - port: http
      path: /probe
      params:
        module: [http_2xx]
      interval: 30s
      scrapeTimeout: 10s
      relabelings:
        - sourceLabels: [__address__]
          targetLabel: __param_target
        - targetLabel: __address__
          replacement: blackbox-exporter.monitoring.svc.cluster.local:9115
