prometheus:
  prometheusSpec:
    additionalScrapeConfigs:
      - job_name: 'blackbox'
        metrics_path: /probe
        params:
          module: [http_2xx]
        static_configs:
          - targets:
              - https://posto3palmeiras.api.postoaki.com/health
              - https://rederoda.api.postoaki.com/health
        relabel_configs:
          - source_labels: [__address__]
            target_label: __param_target
          - target_label: __address__
            replacement: blackbox-exporter-prometheus-blackbox-exporter.monitoring.svc.cluster.local:9115

grafana:
  enabled: true
  adminPassword: Etae7shiu4ho
  datasources:
    default:
      enabled: true
      name: Prometheus
      type: prometheus
      access: proxy
      url: http://prometheus-kube-prometheus-prometheus.monitoring:9090
      isDefault: true

  persistence:
    enabled: true
    size: 10Gi  # Defina o tamanho do PVC conforme necessário
    storageClassName: standard  # Defina o StorageClass do PVC (opcional)
    accessModes:
      - ReadWriteOnce
    annotations: {}  # Você pode adicionar anotações se necessário

  # dashboards:
  #   default:
  #     22128:
  #       gnetId: 22128
  #       revision: 11
  #       datasource: Prometheus
