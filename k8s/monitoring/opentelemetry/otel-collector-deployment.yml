apiVersion: v1
kind: Namespace
metadata:
  name: monitoring
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: otel-collector
  namespace: monitoring
spec:
  replicas: 1
  selector:
    matchLabels:
      app: otel-collector
  template:
    metadata:
      labels:
        app: otel-collector
    spec:
      containers:
        - name: otel-collector
          image: otel/opentelemetry-collector-contrib:0.116.1
          ports:
            - containerPort: 4317 # gRPC
            - containerPort: 55681 # HTTP
          volumeMounts:
            - name: config-volume
              mountPath: /etc/otel-collector-config
          command: ["/otelcol-contrib"]
          args: ["--config", "/etc/otel-collector-config/config.yaml"]
          resources:
            requests:
              memory: "128Mi"
              cpu: "250m"
            limits:
              memory: "512Mi"
              cpu: "500m"
      volumes:
        - name: config-volume
          configMap:
            name: otel-collector-config
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: otel-collector-config
  namespace: monitoring
data:
  config.yaml: |
    receivers:
      otlp:
        protocols:
          grpc:
          http:
    exporters:
      debug:
        verbosity: detailed
      otlp:
        endpoint: grafana-tempo.monitoring.svc.cluster.local:4317
    processors:
      batch:
    service:
      pipelines:
        traces:
          receivers: [otlp]
          processors: [batch]
          exporters: [debug, otlp]

