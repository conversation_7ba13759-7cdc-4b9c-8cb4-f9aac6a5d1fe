#!/bin/bash

# Lista de CRDs necessários para o kube-prometheus-stack
crds=("alertmanagers.monitoring.coreos.com"
"podmonitors.monitoring.coreos.com"
"probes.monitoring.coreos.com"
"prometheuses.monitoring.coreos.com"
"prometheusrules.monitoring.coreos.com"
"servicemonitors.monitoring.coreos.com"
"thanosrulers.monitoring.coreos.com")

# Loop para verificar cada CRD
for crd in ${crds[@]}
do
  if kubectl get crd $crd > /dev/null 2>&1; then
    echo "$crd is installed."
  else
    echo "$crd is NOT installed."
  fi
done