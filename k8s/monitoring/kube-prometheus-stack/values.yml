## Stack name: prometheus-community/kube-prometheus-stack
## Ref: https://github.com/prometheus-community/helm-charts/tree/kube-prometheus-stack-35.5.1/charts/kube-prometheus-stack
##


## Manages Prometheus and Alertmanager components
##
prometheusOperator:
  enabled: true
  admissionWebhooks:
    deployment:
      resources:
        limits:
          cpu: 200m
          memory: 400Mi
        requests:
          cpu: 100m
          memory: 200Mi

## Deploy a Prometheus instance
##
prometheus:
  enabled: true

  ## Example service monitors
  ##
  ## Uncomment the following section to enable ingress-nginx service monitoring
  ##
  # additionalServiceMonitors:
  #   - name: "ingress-nginx-monitor"
  #     selector:
  #       matchLabels:
  #         app.kubernetes.io/name: ingress-nginx
  #     namespaceSelector:
  #       matchNames:
  #         - ingress-nginx
  #     endpoints:
  #       - port: "metrics"


  ## Prometheus StorageSpec for persistent data
  ## ref: https://github.com/prometheus-operator/prometheus-operator/blob/master/Documentation/user-guides/storage.md
  ##
  additionalServiceMonitors:
    - name: "loki-monitor"
      selector:
        matchLabels:
          app.kubernetes.io/instance: loki
          app.kubernetes.io/name: loki
      namespaceSelector:
        matchNames:
          - loki-stack
      endpoints:
        - port: "http-metrics"
  additionalPodMonitors:
    - name: "promtail-monitor"
      selector:
        matchLabels:
          app.kubernetes.io/instance: promtail
          app.kubernetes.io/name: promtail
      namespaceSelector:
        matchNames:
          - loki-stack
      podMetricsEndpoints:
        - port: "http-metrics"

  prometheusSpec:
    retention: 15d
    storageSpec:
      volumeClaimTemplate:
        spec:
          storageClassName: do-block-storage
          accessModes: ["ReadWriteOnce"]
          resources:
            requests:
              storage: 50Gi


nodeExporter:
  enabled: true
  resources:
    limits:
      cpu: 200m
      memory: 50Mi
    requests:
      cpu: 100m
      memory: 30Mi  

## Configuration for Grafana
## ref: https://grafana.com/
##
## Deploy a Grafana instance
##
grafana:
  enabled: true
  adminPassword: bxtNZ055TTB!5ICA
  persistence:
    enabled: true
    storageClassName: do-block-storage
    accessModes: ["ReadWriteOnce"]
    size: 5Gi
  resources:
    limits:
      cpu: 1000m
      memory: 2Gi
    requests:
      cpu: 500m
      memory: 1Gi

## Configuration for Alertmanager
## ref: https://prometheus.io/docs/alerting/alertmanager/
##
## Deploy an Alertmanager instance
##
alertmanager:
  enabled: true


## Create default rules for monitoring the cluster
##
## Disable `etcd` and `kubeScheduler` rules (managed by DOKS, so metrics are not accessible)
##
defaultRules:
  create: true
  rules:
    etcd: false
    kubeScheduler: false

## Component scraping kube scheduler
##
## Disabled because it's being managed by DOKS, so it's not accessible
##
kubeScheduler:
  enabled: false

## Component scraping etcd
##
## Disabled because it's being managed by DOKS, so it's not accessible
##
kubeEtcd:
  enabled: false