metricsGenerator:
  enabled: true
  remoteWriteUrl: "http://kube-prometheus-stack-prometheus.kube-prometheus-stack.svc.cluster.local:9090/api/v1/write"
retention: 24h
server:
  http_listen_port: 3100
reportingEnabled: false
storage:
  trace:
    backend: s3
    s3:
      bucket: loki
      endpoint: "https://objectstorage.sa-saopaulo-1.oraclecloud.com"
      region: sa-saopaulo-1
      access_key: 
      secret_key: 
traces:
  otlp:
    grpc:
      enabled: true
    http:
      enabled: true
  zipkin:
    enabled: false
  jaeger:
    thriftHttp:
      enabled: true
  opencensus:
    enabled: false