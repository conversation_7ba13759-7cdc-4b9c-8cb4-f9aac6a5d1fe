#!/bin/bash

kubectl create namespace redis
kubectl config set-context --current --namespace=redis
VERSION=$(curl --silent https://api.github.com/repos/RedisLabs/redis-enterprise-k8s-docs/releases/latest | grep tag_name | awk -F'"' '{print $4}')

echo "Installing Redis Enterprise Operator version $VERSION"
kubectl apply -f https://raw.githubusercontent.com/RedisLabs/redis-enterprise-k8s-docs/$VERSION/bundle.yaml

echo "Waiting for Redis Enterprise Operator to be ready"
kubectl get deployment redis-enterprise-operator


echo "Installing Redis Enterprise Cluster"
kubectl apply -f rec.yaml
kubectl get rec
kubectl rollout status sts/redis-enterprise-cluster
