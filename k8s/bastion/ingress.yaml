apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: bastion-ingress
  namespace: bastion
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/backend-protocol: "TCP"
    nginx.ingress.kubernetes.io/ssl-passthrough: "true"
    nginx.ingress.kubernetes.io/tcp-services-configmap: "ingress-nginx/tcp-services"
    nginx.ingress.kubernetes.io/service-upstream: "true"
spec:
  ingressClassName: nginx
  rules:
  - host: bastion.postoaki.app
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: bastion
            port:
              number: 22