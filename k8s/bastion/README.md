# Bastion Host for PostoAki Kubernetes Cluster

This directory contains Kubernetes manifests for deploying a bastion host in the cluster.

## Purpose

The bastion host provides a secure entry point to the Kubernetes cluster, allowing administrators to:

- Connect to cluster resources that are not exposed externally
- Perform administrative tasks within the cluster
- Access databases and other internal services
- Execute maintenance scripts

## Deployment

1. First, create namespace and a secret with your SSH public key:

```bash
kubectl create namespace bastion
kubectl create secret generic bastion-ssh-keys --from-file=authorized_keys=$HOME/.ssh/id_rsa.pub -n bastion
```

2. Apply the Kubernetes manifests:

```bash
kubectl apply -f k8s/bastion/
```

kubectl create configmap tcp-services -n ingress-nginx --from-literal=22="bastion/bastion:22"

3. Connect to the bastion using one of the following methods:

### Method 1: Using the Ingress (recommended)

The bastion is accessible via the domain `bastion.postoaki.app`. You can connect directly using SSH:

```bash
ssh <EMAIL>
```

### Method 2: Using kubectl port-forward

If the Ingress is not available or you prefer to use port-forwarding:

```bash
# Get the pod name
BASTION_POD=$(kubectl get pods -n bastion -l app=bastion -o jsonpath='{.items[0].metadata.name}')

# Forward a local port to the bastion
kubectl port-forward $BASTION_POD 2222:22 -n bastion
```

Then, in a new terminal, connect via SSH:

```bash
ssh -p 2222 root@localhost
```
