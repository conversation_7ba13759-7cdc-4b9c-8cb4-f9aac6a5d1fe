apiVersion: apps/v1
kind: Deployment
metadata:
  name: bastion
  namespace: bastion
  labels:
    app: bastion
spec:
  replicas: 1
  selector:
    matchLabels:
      app: bastion
  template:
    metadata:
      labels:
        app: bastion
    spec:
      containers:
      - name: bastion
        image: keviocastro/bastion:ubuntu-20
        ports:
        - containerPort: 22
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "300m"
        volumeMounts:
        - name: ssh-keys
          mountPath: /root/.ssh/authorized_keys
          subPath: authorized_keys
          readOnly: true
        - name: ssh-config
          mountPath: /etc/ssh/sshd_config
          subPath: sshd_config
      volumes:
      - name: ssh-keys
        secret:
          secretName: bastion-ssh-keys
          defaultMode: 0600
      - name: ssh-config
        configMap:
          name: bastion-config