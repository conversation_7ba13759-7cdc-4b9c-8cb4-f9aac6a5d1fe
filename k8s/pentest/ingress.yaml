apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: api-ingress
  namespace: pentest
spec:
  ingressClassName: nginx
  rules:
    - host: "api-pentest.postoaki.app"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: api-service
                port:
                  number: 8080
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: painel-ingress
  namespace: pentest
spec:
  ingressClassName: nginx
  rules:
    - host: "painel-pentest.postoaki.app"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: painel-service
                port:
                  number: 80
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: painel-frota-ingress
  namespace: pentest
spec:
  ingressClassName: nginx
  rules:
    - host: "painelfrota-pentest.postoaki.app"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: painel-frota-service
                port:
                  number: 80
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: painel-venda-v2-ingress
  namespace: pentest
spec:
  ingressClassName: nginx
  rules:
    - host: "painelvenda-pentest.postoaki.app"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: painel-venda-v2-service
                port:
                  number: 80