apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: api-ingress
  namespace: hml
spec:
  ingressClassName: nginx
  rules:
    - host: "api-hml.postoaki.app"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: api-service
                port:
                  number: 8080
    - host: "apihomolog.postoaki.com.br"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: api-service
                port:
                  number: 8080
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: painel-ingress
  namespace: hml
spec:
  ingressClassName: nginx
  rules:
    - host: "painel-hml.postoaki.app"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: painel-service
                port:
                  number: 80
    - host: "painelhomolog.postoaki.com.br"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: painel-service
                port:
                  number: 80
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: painel-frota-ingress
  namespace: hml
spec:
  ingressClassName: nginx
  rules:
    - host: "painelfrota-hml.postoaki.app"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: painel-frota-service
                port:
                  number: 80
    - host: "painelfrotahomolog.postoaki.com.br"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: painel-frota-service
                port:
                  number: 80
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: painel-venda-v2-ingress
  namespace: hml
spec:
  ingressClassName: nginx
  rules:
    - host: "painelvendahomolog.postoaki.com.br"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: painel-venda-v2-service
                port:
                  number: 80
    - host: "painelvenda-hml.postoaki.app"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: painel-venda-v2-service
                port:
                  number: 80