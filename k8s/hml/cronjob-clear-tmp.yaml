apiVersion: v1
kind: ConfigMap
metadata:
  name: send-req-logs-script
  namespace: hml
data:
  clear-tmp.sh: |
    #!/bin/sh

    NAMESPACE="hml"
    LABEL="app=api"

    echo "Getting the first pod with label $LABEL in namespace $NAMESPACE..."
    POD=$(kubectl get pods -n $NAMESPACE -l $LABEL -o jsonpath='{.items[0].metadata.name}')

    if [ -n "$POD" ]; then
      echo "Processing pod $POD..."

      echo "Removing files of dir /tmp/* from pod $POD..."
      kubectl exec -n $NAMESPACE $POD -- rm -rf /tmp/*
    else
      echo "No pods found with label $LABEL in namespace $NAMESPACE."
    fi
---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: clear-tmp-dri
  namespace: hml
spec:
  schedule: "0 * * * *"
  timeZone: "America/Sao_Paulo"
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: clear-tmp-dir
              image: bitnami/kubectl:latest
              imagePullPolicy: Always
              command: ["/bin/sh", "-c", "/scripts/clear-tmp.sh"]
              volumeMounts:
                - name: script-volume
                  mountPath: /scripts
          restartPolicy: Never
          volumes:
            - name: script-volume
              configMap:
                name: send-req-logs-script
                defaultMode: 0775
          serviceAccountName: new-copy-log-sa
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: new-copy-log-sa
  namespace: hml
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: hml
  name: new-pod-reader
rules:
  - apiGroups: [""]
    resources: ["pods", "pods/exec", "pods/log"]
    verbs: ["get", "list", "exec"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: new-read-pods
  namespace: hml
subjects:
  - kind: ServiceAccount
    name: new-copy-log-sa
    namespace: hml
roleRef:
  kind: Role
  name: new-pod-reader
  apiGroup: rbac.authorization.k8s.io
