# TODO: excluir. Foi criado o pvc-2 porque este deu problema
apiVersion: v1
kind: PersistentVolume
metadata:
  name: file-storage-pv
  namespace: api
  labels:
    app: shared-storage
spec:
  capacity:
    storage: 10Gi
  accessModes:
    - ReadWriteMany
  nfs:
    path: "/FileSystem-Api-tmp"
    server: 192.168.202.92
  persistentVolumeReclaimPolicy: Retain
  storageClassName: "nfs-storage"
---
# TODO: excluir. Foi criado o pvc-2 porque este deu problema
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: file-storage-pvc
  namespace: api
  labels:
    app: shared-storage
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 10Gi
  storageClassName: "nfs-storage"
---
apiVersion: v1
kind: PersistentVolume
metadata:
  name: file-storage-pv-2
  namespace: api
  labels:
    app: shared-storage
spec:
  capacity:
    storage: 10Gi
  accessModes:
    - ReadWriteMany
  nfs:
    path: "/FileSystem-Api-tmp"
    server: 192.168.202.92
  persistentVolumeReclaimPolicy: Retain
  storageClassName: "nfs-storage"
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: file-storage-pvc-2
  namespace: api
  labels:
    app: shared-storage
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 10Gi
  storageClassName: "nfs-storage"
