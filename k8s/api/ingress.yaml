apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: api-ingress
  namespace: api
spec:
  ingressClassName: nginx
  rules:

    - host: "*.api.postoaki.com"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: api-service
                port:
                  number: 8080
    - host: "api.postoaki.com"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: api-service
                port:
                  number: 8080
    - host: "scheduled.api.postoaki.com"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: api-schedule
                port:
                  number: 8080
    - host: "painel.api.postoaki.net"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: api-schedule
                port:
                  number: 8080
    - host: "www.postoaki.com"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: api-service
                port:
                  number: 8080

