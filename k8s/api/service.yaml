apiVersion: v1
kind: Service
metadata:
  name: api-service
  namespace: api
spec:
  selector:
    app: api
  ports:
    - protocol: TCP
      port: 8080
      targetPort: 8080
  type: LoadBalancer
---
apiVersion: v1
kind: Service
metadata:
  name: api-schedule
  namespace: api
spec:
  selector:
    app: api-schedule
  ports:
    - protocol: TCP
      port: 8080
      targetPort: 8080
  type: LoadBalancer