apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: api-ingress
  namespace: game
spec:
  ingressClassName: nginx
  rules:
    - host: "api-game-hml.postoaki.app"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: api-service
                port:
                  number: 8080
    - host: "apigameficacao.postoaki.com.br"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: api-service
                port:
                  number: 8080
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: painel-ingress
  namespace: game
spec:
  ingressClassName: nginx
  rules:
    - host: "painel-game-hml.postoaki.app"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: painel-service
                port:
                  number: 80
    - host: "painelgameficacao.postoaki.com.br"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: painel-service
                port:
                  number: 80
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: painel-frota-ingress
  namespace: game
spec:
  ingressClassName: nginx
  rules:
    - host: "painelfrota-game-hml.postoaki.app"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: painel-frota-service
                port:
                  number: 80
    - host: "painelfrotagameficacao.postoaki.com.br"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: painel-frota-service
                port:
                  number: 80
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: painel-venda-v2-ingress
  namespace: game
spec:
  ingressClassName: nginx
  rules:
    - host: "painelvenda-game-hml.postoaki.app"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: painel-venda-v2-service
                port:
                  number: 80
    - host: "painelvendagameficacao.postoaki.com.br"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: painel-venda-v2-service
                port:
                  number: 80