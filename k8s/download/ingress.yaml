apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: file-server-ingress
  namespace: download
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
spec:
  ingressClassName: nginx
  rules:
  - host: download.postoaki.com.br
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: file-server-service
            port:
              number: 80
