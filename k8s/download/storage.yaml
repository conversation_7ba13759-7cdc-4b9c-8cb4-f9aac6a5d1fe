apiVersion: v1
kind: PersistentVolume
metadata:
  name: file-server-pv
  namespace: download
  labels:
    app: file-server
spec:
  capacity:
    storage: 10Gi
  accessModes:
    - ReadWriteMany
  nfs:
    path: "/FileSystem-download"
    server: 192.168.202.92
  persistentVolumeReclaimPolicy: Retain
  storageClassName: "nfs-storage"
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: file-server-pvc
  namespace: download
  labels:
    app: file-server
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 10Gi
  storageClassName: "nfs-storage"