apiVersion: apps/v1
kind: Deployment
metadata:
  name: file-server
  namespace: download
spec:
  replicas: 1
  selector:
    matchLabels:
      app: file-server
  template:
    metadata:
      labels:
        app: file-server
    spec:
      containers:
      - name: nginx
        image: nginx:latest
        volumeMounts:
        - name: arquivos
          mountPath: /usr/share/nginx/html
        - name: nginx-config
          mountPath: /etc/nginx/conf.d/default.conf
          subPath: nginx.conf
      volumes:
      - name: arquivos
        persistentVolumeClaim:
          claimName: file-server-pvc
      - name: nginx-config
        configMap:
          name: nginx-config
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-config
  namespace: download
data:
  nginx.conf: |
    server {
        listen 80;
        server_name localhost;
        
        location / {
            root /usr/share/nginx/html;
            autoindex on;
            autoindex_exact_size on;
            autoindex_localtime on;
        }
    }
