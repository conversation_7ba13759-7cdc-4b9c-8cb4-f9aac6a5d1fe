apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: service-preco-ingress
  namespace: preco
spec:
  ingressClassName: nginx
  rules:
    - host: "apiservico.postoaki.com"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: servico-preco
                port:
                  number: 8080
    - host: "apiservico.postoaki.com.br"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: servico-preco
                port:
                  number: 8080
    - host: "servicopreco.api.postoaki.app"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: servico-preco
                port:
                  number: 8080