apiVersion: v1
kind: ConfigMap
metadata:
  name: preco-db-scripts
  namespace: preco
data:
  db-init.sh: |
    #!/bin/bash
    set -e

    # export PGPASSWORD=BIXlmXa7g1Lv8uVNc54fxSKhqSH8svS3
    # export DB_NAME=postoakiservico
    # DB_HOST=postgres.postgres.svc.cluster.local
    # DB_PORT=5432
    # DB_USER=postgres
    # DB_USER_PRECO=postoakiservico

    export PGPASSWORD="4FXx(xCp70E!AH_rjknv)kjB"
    export DB_NAME=postoakipreco
    DB_HOST=***************
    DB_PORT=5000
    DB_USER=usr_preco
    DB_USER_PRECO=usr_preco
    
    echo 'Parando todas as sessões do banco de dados'
    # Usar pg_terminate_backend em vez de pg_cancel_backend
    psql -h ${DB_HOST} -p ${DB_PORT} -U ${DB_USER} -d postgres -c "SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE datname = '${DB_NAME}' AND pid <> pg_backend_pid()"
    
    # Aguardar um momento para garantir que todas as conexões sejam encerradas
    sleep 5
    
    echo 'Excluindo banco de dados'
    psql -h ${DB_HOST} -p ${DB_PORT} -U ${DB_USER} -d postgres -c "DROP DATABASE IF EXISTS ${DB_NAME}"
    
    echo 'Criando novo banco de dados'
    psql -h ${DB_HOST} -p ${DB_PORT} -U ${DB_USER} -d postgres -c "CREATE DATABASE ${DB_NAME}"
    
    echo 'Concedendo permissões ao usuário'
    psql -h ${DB_HOST} -p ${DB_PORT} -U ${DB_USER} -d postgres -c "GRANT ALL PRIVILEGES ON DATABASE ${DB_NAME} TO ${DB_USER_PRECO}"
    
    # Conectar ao banco de dados recém-criado para conceder permissões em nível de esquema
    psql -h ${DB_HOST} -p ${DB_PORT} -U ${DB_USER} -d ${DB_NAME} -c "GRANT ALL PRIVILEGES ON SCHEMA public TO ${DB_USER_PRECO}"
    psql -h ${DB_HOST} -p ${DB_PORT} -U ${DB_USER} -d ${DB_NAME} -c "ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL PRIVILEGES ON TABLES TO ${DB_USER_PRECO}"
    psql -h ${DB_HOST} -p ${DB_PORT} -U ${DB_USER} -d ${DB_NAME} -c "ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL PRIVILEGES ON SEQUENCES TO ${DB_USER_PRECO}"
    psql -h ${DB_HOST} -p ${DB_PORT} -U ${DB_USER} -d ${DB_NAME} -c "ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL PRIVILEGES ON FUNCTIONS TO ${DB_USER_PRECO}"

    echo 'Banco de dados inicializado com sucesso'

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: servico-preco-java-opts
  namespace: preco
data:
  JAVA_OPTS: -Dsun.jnu.encoding=UTF8 -Dfile.encoding=UTF8 -Dfile.encoding.pkg=sun.io -Dsun.io.unicode.encoding=UnicodeLittle -Duser.country=BR -Duser.language=pt -Duser.timezone=GMT-0300 -Ddebug=false -Dspring.datasource.password="4FXx(xCp70E!AH_rjknv)kjB" -Dspring.datasource.url=**************************************************** -Dspring.datasource.username=usr_preco -Dspring.jpa.hibernate.ddl-auto=none -Dspring.jpa.show-sql=false -Duser.timezone=Americas/Sao_Paulo -Dspring.datasource.hikari.maximum-pool-size=30 -Dpostoaki.api=http://servicopreco.api.postoaki.com/api/v2