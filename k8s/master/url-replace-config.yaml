apiVersion: v1
kind: ConfigMap
metadata:
  name: url-replace-script
  namespace: master
data:
  replace-urls.sh: |
    #!/bin/bash
    
    exec > /tmp/url-replace.log 2>&1
    
    echo "Starting URL replacement in /usr/share/nginx/html"
    
    # Replace production URLs with homologation URLs
    find /usr/share/nginx/html -type f -name "*.html" -o -name "*.js" -o -name "*.json" -o -name "*.css" | xargs sed -i \
      -e 's|scheduled.api.postoaki.com|api-master-hml.postoaki.app|g' \
      -e 's|painel.api.postoaki.com|api-master-hml.postoaki.app|g' \
      -e 's|painelvenda.api.postoaki.com|api-master-hml.postoaki.app|g' \
      -e 's|painelvenda.postoaki.com.br=|painelvenda-hml.postoaki.app|g' \
      -e 's|api.postoaki.com|api-master-hml.postoaki.app|g'
    
    echo "URL replacement completed"