apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: api-ingress
  namespace: master
spec:
  ingressClassName: nginx
  rules:
    - host: "api-master-hml.postoaki.app"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: api
                port:
                  number: 8080
    - host: "apimaster.postoaki.com.br"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: api
                port:
                  number: 8080
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: painel-ingress
  namespace: master
spec:
  ingressClassName: nginx
  rules:
    - host: "painel-master-hml.postoaki.app"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: painel
                port:
                  number: 80
    - host: "painelmaster.postoaki.com.br"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: painel
                port:
                  number: 80
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: painel-frota-ingress
  namespace: master
spec:
  ingressClassName: nginx
  rules:
    - host: "painelfrota-master-hml.postoaki.app"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: painel-frota
                port:
                  number: 80
    - host: "painelfrotamaster.postoaki.com.br"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: painel-frota
                port:
                  number: 80
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: painel-venda-v2-ingress
  namespace: master
spec:
  ingressClassName: nginx
  rules:
    - host: "painelvenda-master-hml.postoaki.app"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: painel-venda-v2
                port:
                  number: 80
    - host: "painelvendamaster.postoaki.com.br"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: painel-venda-v2
                port:
                  number: 80