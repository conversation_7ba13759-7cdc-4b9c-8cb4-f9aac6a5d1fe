apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: painel-hpa
  namespace: painel
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: painel-deployment
  minReplicas: 1
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 90
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 15
    scaleUp:
      stabilizationWindowSeconds: 0
      policies:
      - type: Percent
        value: 100
        periodSeconds: 15