apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: painel-ingress
  namespace: painel
spec:
  ingressClassName: nginx
  rules:
    - host: "painel.postoaki.app"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: painel-service
                port:
                  number: 80
    - host: "painel.postoaki.com.br"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: painel-service
                port:
                  number: 80