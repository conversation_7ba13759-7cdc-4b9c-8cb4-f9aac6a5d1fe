apiVersion: apps/v1
kind: Deployment
metadata:
  name: wiki-deployment
  namespace: painel
spec:
  replicas: 1
  selector:
    matchLabels:
      app: wiki
  template:
    metadata:
      labels:
        app: wiki
    spec:
      containers:
        - name: wiki
          image: gru.ocir.io/grbvypj1mx3p/postoaki-wiki:latest
          imagePullPolicy: Always
          ports:
            - containerPort: 3000
          resources:
            limits:
                memory: "512Mi"
                cpu: "500m"
          livenessProbe:
            httpGet:
              path: /
              port: 3000
            initialDelaySeconds: 30
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /
              port: 3000
            initialDelaySeconds: 30
            periodSeconds: 10
      imagePullSecrets:
        - name: docker-registry

