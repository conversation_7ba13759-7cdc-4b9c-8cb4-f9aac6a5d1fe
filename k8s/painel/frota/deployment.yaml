apiVersion: apps/v1
kind: Deployment
metadata:
  name: painel-frota-deployment
  namespace: painel
spec:
  replicas: 1
  selector:
    matchLabels:
      app: painel-frota
  template:
    metadata:
      labels:
        app: painel-frota
    spec:
      containers:
        - name: painel-frota
          image: gru.ocir.io/grbvypj1mx3p/postoaki-painel-frota:13894
          imagePullPolicy: Always
          ports:
            - containerPort: 80
          resources:
            limits:
              memory: "1Gi"
              cpu: "1"
          livenessProbe:
            httpGet:
              path: /
              port: 80
            initialDelaySeconds: 60
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /
              port: 80
            initialDelaySeconds: 60
            periodSeconds: 10
      imagePullSecrets:
        - name: docker-registry