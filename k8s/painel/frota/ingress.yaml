apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: painel-frota-ingress
  namespace: painel
spec:
  ingressClassName: nginx
  rules:
    - host: "painelfrota.postoaki.com.br"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: painel-frota-service
                port:
                  number: 80
    - host: "painelfrota.postoaki.app"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: painel-frota-service
                port:
                  number: 80
