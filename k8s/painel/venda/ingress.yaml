apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: painel-venda-v2-ingress
  namespace: painel
spec:
  ingressClassName: nginx
  rules:
    - host: "painelvenda.postoaki.com.br"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: painel-venda-v2-service
                port:
                  number: 80
    - host: "painelvenda.postoaki.app"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: painel-venda-v2-service
                port:
                  number: 80