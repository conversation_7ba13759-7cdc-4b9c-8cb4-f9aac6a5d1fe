apiVersion: apps/v1
kind: Deployment
metadata:
  name: redirect-deployment
  namespace: painel
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redirect
  template:
    metadata:
      labels:
        app: redirect
    spec:
      containers:
        - name: redirect
          image: gru.ocir.io/grbvypj1mx3p/postoaki-redirect:latest
          imagePullPolicy: Always
          ports:
            - containerPort: 80
          resources:
            limits:
                memory: "512Mi"
                cpu: "500m"
          livenessProbe:
            httpGet:
              path: /
              port: 80
            initialDelaySeconds: 10
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /
              port: 80
            initialDelaySeconds: 10
            periodSeconds: 10
      imagePullSecrets:
        - name: docker-registry