apiVersion: apps/v1
kind: Deployment
metadata:
  name: wordpress-lp
  namespace: site
  labels:
    app: wordpress-lp
spec:
  replicas: 1
  selector:
    matchLabels:
      app: wordpress-lp
  template:
    metadata:
      labels:
        app: wordpress-lp
    spec:
      containers:
      - name: wordpress-lp
        image: wordpress:6.7.2
        ports:
        - containerPort: 80
        env:
        - name: WP_DEBUG
          value: "true"
        - name: WP_DEBUG_LOG
          value: "true"
        - name: WP_DEBUG_DISPLAY
          value: "true"
        - name: WORDPRESS_CONFIG_EXTRA
          value: |
            define('WP_HOME', 'https://lp.postoaki.com.br');
            define('WP_SITEURL', 'https://lp.postoaki.com.br');
            define('FORCE_SSL_ADMIN', true);
            define('WP_CONTENT_URL', 'https://lp.postoaki.com.br/wp-content');
            $_SERVER['HTTPS'] = 'on';
            define('WP_DEBUG_DISPLAY', true);
            define('WP_DEBUG_LOG', true);
            define('SCRIPT_DEBUG', true);
            ini_set('display_errors', 1);
            ini_set('log_errors', 1);
            ini_set('error_reporting', E_ALL);
        - name: WORDPRESS_DEBUG
          value: "1"
        - name: WORDPRESS_DB_HOST
          value: "mariadb-service"
        - name: DB_HOST
          value: "mariadb-service"
        - name: WORDPRESS_DB_USER
          value: "wordpress_lp"
        - name: WORDPRESS_DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: mariadb-lp-secret
              key: mariadb-user-password
        - name: WORDPRESS_DB_NAME
          value: "wordpress_lp"
        volumeMounts:
        - name: wordpress-lp-storage
          mountPath: "/var/www/html"
        - name: php-config
          mountPath: /usr/local/etc/php/conf.d/upload.ini
          subPath: upload.ini
      volumes:
      - name: wordpress-lp-storage
        persistentVolumeClaim:
          claimName: wordpress-lp-storage-pvc
      - name: php-config
        configMap:
          name: wordpress-php-config
          items:
          - key: upload.ini
            path: upload.ini
