apiVersion: v1
kind: ConfigMap
metadata:
  name: wordpress-mu-plugin
  namespace: site
data:
  disable-canonical-redirect.php: |
    <?php
    /**
     * Plugin Name: Disable Canonical Redirect
     * Description: Desabilita o redirecionamento canônico automático do WordPress
     * Version: 1.0
     * Author: Auto-generated
     */
    
    // Desabilitar redirecionamento canônico se a flag global estiver definida
    if (isset($GLOBALS['disable_canonical_redirect']) && $GLOBALS['disable_canonical_redirect']) {
        add_action('init', function() {
            remove_action('template_redirect', 'redirect_canonical');
        });
    }
