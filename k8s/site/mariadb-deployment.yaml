apiVersion: apps/v1
kind: Deployment
metadata:
  name: mariadb
  namespace: site
  labels:
    app: mariadb
spec:
  replicas: 1
  selector:
    matchLabels:
      app: mariadb
  template:
    metadata:
      labels:
        app: mariadb
    spec:
      containers:
      - name: mariadb
        image: mariadb:11.4
        ports:
        - containerPort: 3306
        env:
        - name: MYSQL_ROOT_PASSWORD
          valueFrom:
            secretKeyRef:
              name: mariadb-secret
              key: mariadb-root-password
        - name: MYSQL_DATABASE
          value: "wordpress"
        - name: MYSQL_USER
          value: "wordpress"
        - name: MYSQL_PASSWORD
          valueFrom:
            secretKeyRef:
              name: mariadb-secret
              key: mariadb-user-password
        volumeMounts:
        - name: mariadb-storage
          mountPath: "/var/lib/mysql"
      volumes:
      - name: mariadb-storage
        persistentVolumeClaim:
          claimName: mariadb-storage-pvc