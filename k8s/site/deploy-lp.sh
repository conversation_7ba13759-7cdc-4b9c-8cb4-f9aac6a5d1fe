#!/bin/bash

echo "Aplicando recursos para WordPress Landing Page..."

# Aplicar secret
kubectl apply -f mariadb-lp-secret.yaml

# Aplicar PVC
kubectl apply -f wordpress-lp-persistent-volume.yaml

# Aplicar deployment do WordPress
kubectl apply -f wordpress-lp-deployment.yaml

# Aplicar service
kubectl apply -f wordpress-lp-service.yaml

# Aplicar ingress
kubectl apply -f wordpress-lp-ingress.yaml

# Atualizar ingress principal (removendo domínio lp.postoaki.com.br)
kubectl apply -f wordpress-ingress.yaml

echo "Recursos aplicados com sucesso!"
echo ""
echo "Para criar a database no MariaDB existente, execute:"
echo "kubectl exec -it deployment/mariadb -n site -- mysql -u root -p < create-lp-database.sql"
echo "Senha root: f0U2urj9[h}w"
echo "Nova senha do usuário wordpress_lp: 9aZ!r7wX@Q3#jM2"
