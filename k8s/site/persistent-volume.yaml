apiVersion: v1
kind: PersistentVolume
metadata:
  name: wordpress-storage-pv
  namespace: site
  labels:
    app: wordpress-storage
spec:
  capacity:
    storage: 50Gi
  accessModes:
    - ReadWriteMany
  nfs:
    path: "/FileSystem-Wordpress"
    server: 192.168.200.249
  persistentVolumeReclaimPolicy: Retain
  storageClassName: "nfs-storage"
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: wordpress-storage-pvc
  namespace: site
  labels:
    app: wordpress-storage
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 50Gi
  storageClassName: "nfs-storage"