apiVersion: v1
kind: PersistentVolume
metadata:
  name: mariadb-storage-pv
  namespace: site
  labels:
    app: mariadb-storage
spec:
  capacity:
    storage: 10Gi
  accessModes:
    - ReadWriteMany
  nfs:
    path: "/FileSystem-MariaDB"
    server: 192.168.200.249
  persistentVolumeReclaimPolicy: Retain
  storageClassName: "nfs-storage"
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: mariadb-storage-pvc
  namespace: site
  labels:
    app: mariadb-storage
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 10Gi
  storageClassName: "nfs-storage"