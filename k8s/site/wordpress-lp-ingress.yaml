apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: wordpress-lp-ingress
  namespace: site
  annotations:
    nginx.ingress.kubernetes.io/from-to-www-redirect: "false"
spec:
  ingressClassName: nginx
  rules:
  - host: lp.postoaki.com.br
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: wordpress-lp-service
            port:
              number: 80
