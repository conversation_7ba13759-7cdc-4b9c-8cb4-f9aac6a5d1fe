apiVersion: apps/v1
kind: Deployment
metadata:
  name: wordpress
  namespace: site
  labels:
    app: wordpress
spec:
  replicas: 1
  selector:
    matchLabels:
      app: wordpress
  template:
    metadata:
      labels:
        app: wordpress
    spec:
      containers:
      - name: wordpress
        image: wordpress:6.7.2
        ports:
        - containerPort: 80
        env:
        - name: WORDPRESS_CONFIG_EXTRA
          value: |
            define('WP_HOME', 'https://www.postoaki.com.br');
            define('WP_SITEURL', 'https://www.postoaki.com.br');
            define('FORCE_SSL_ADMIN', true);
            define('WP_CONTENT_URL', 'https://www.postoaki.com.br/wp-content');
            $_SERVER['HTTPS'] = 'on';
        - name: WORDPRESS_DB_HOST
          value: "mariadb-service"
        - name: WORDPRESS_DB_USER
          value: "wordpress"
        - name: WORDPRESS_DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: mariadb-secret
              key: mariadb-user-password
        - name: WORDPRESS_DB_NAME
          value: "wordpress"
        volumeMounts:
        - name: wordpress-storage
          mountPath: "/var/www/html"
        - name: php-config
          mountPath: /usr/local/etc/php/conf.d/upload.ini
          subPath: upload.ini
      volumes:
      - name: wordpress-storage
        persistentVolumeClaim:
          claimName: wordpress-storage-pvc
      - name: php-config
        configMap:
          name: wordpress-php-config
          items:
          - key: upload.ini
            path: upload.ini
