apiVersion: apps/v1
kind: Deployment
metadata:
  name: servico-bi
  namespace: bi
spec:
  replicas: 1
  selector:
    matchLabels:
      app: servico-bi
  template:
    metadata:
      labels:
        app: servico-bi
    spec:
      containers:
        - name: servico-bi
          image: metabase/metabase
          imagePullPolicy: Always
          ports:
            - containerPort: 8080
          env:
            - name: TZ
              value: "America/Sao_Paulo"
            - name: MB_DB_TYPE
              value: "postgres"
            - name: MB_DB_DBNAME
              value: "postoakibi"
            - name: MB_DB_PORT
              value: "5000"
            - name: MB_DB_USER
              value: "usr_bi"
            - name: MB_DB_PASS
              value: "D(ORieJQz!5LrW9ozX)h9yR"
            - name: MB_DB_HOST
              value: "***************"
            - name: MB_CONTENT_SECURITY_POLICY
              value: "default-src 'none'; frame-ancestors 'self' https://bi.postoaki.com.br capacitor://localhost ionic://localhost;"
            - name: MB_FRAME_OPTIONS
              value: "ALLOWALL"
            # Configurações de memória JVM
            - name: JAVA_OPTS
              value: "-Xmx5g -Xms2g -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+UnlockExperimentalVMOptions"
            # Configurações específicas do Metabase para performance
            - name: MB_JETTY_MAX_THREADS
              value: "100"
            - name: MB_ASYNC_QUERY_THREAD_POOL_SIZE
              value: "50"
            - name: MB_APPLICATION_DB_MAX_CONNECTION_POOL_SIZE
              value: "15"
          resources:
            limits:
              memory: "6Gi"
              cpu: "2"
            requests:
              memory: "2Gi"
              cpu: "500m"
          livenessProbe:
            httpGet:
              path: /api/health
              port: 3000
            initialDelaySeconds: 120
            periodSeconds: 30
            timeoutSeconds: 10
            failureThreshold: 5
          readinessProbe:
            httpGet:
              path: /api/health
              port: 3000
            initialDelaySeconds: 60
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
      imagePullSecrets:
        - name: docker-registry
