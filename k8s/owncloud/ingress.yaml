apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: owncloud
  namespace: owncloud
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/proxy-body-size: "1g"
spec:
  ingressClassName: nginx
  rules:
  - host: drive2.postoaki.com.br
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: owncloud
            port:
              number: 8080
  - host: drive2.postoaki.app
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: owncloud
            port:
              number: 8080