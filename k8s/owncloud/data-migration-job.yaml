apiVersion: batch/v1
kind: Job
metadata:
  name: owncloud-data-migration
  namespace: owncloud
spec:
  template:
    spec:
      containers:
      - name: data-migration
        image: busybox:latest
        command: ["/bin/sh", "-c"]
        args:
        - |
          echo "Iniciando migração de dados..."
          echo "Copiando dados de /source para /destination..."
          cp -av /source/* /destination/
          echo "Migração concluída com sucesso!"
        volumeMounts:
        - name: source-volume
          mountPath: /source
        - name: destination-volume
          mountPath: /destination
      restartPolicy: Never
      volumes:
      - name: source-volume
        persistentVolumeClaim:
          claimName: owncloud-pvc
      - name: destination-volume
        persistentVolumeClaim:
          claimName: owncloud-pvc-standard
  backoffLimit: 2