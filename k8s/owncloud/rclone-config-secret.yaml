apiVersion: v1
kind: Secret
metadata:
  name: rclone-config
  namespace: owncloud
type: Opaque
stringData:
  rclone.conf: |
    [oci]
    type = s3
    provider = Other
    access_key_id = 3e974592f1b2eaaf9463ae410465d52ea1a2192e
    secret_access_key = sYETw3bNV9WIS90kxV0Qxx45ace8+TmcEdw5bIVbmyc=
    region = sa-saopaulo-1
    endpoint = https://grbvypj1mx3p.compat.objectstorage.sa-saopaulo-1.oraclecloud.com
    force_path_style = true
    no_check_certificate = true
    bucket_acl = private
