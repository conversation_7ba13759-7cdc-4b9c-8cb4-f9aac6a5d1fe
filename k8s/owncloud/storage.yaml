apiVersion: v1
kind: PersistentVolume
metadata:
  name: owncloud-pv
  namespace: owncloud
  labels:
    app: owncloud-storage
spec:
  capacity:
    storage: 2Ti
  accessModes:
    - ReadWriteMany
  nfs:
    path: "/FileSystem-Owncloud"
    server: 192.168.202.92
  persistentVolumeReclaimPolicy: Retain
  storageClassName: "nfs-storage"
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: owncloud-pvc
  namespace: owncloud
  labels:
    app: owncloud-storage
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 2Ti
  storageClassName: "nfs-storage"
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: owncloud-pvc-standard
  namespace: owncloud
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: oci-bv  # Classe de armazenamento econômica da Oracle Cloud
  resources:
    requests:
      storage: 2Ti 