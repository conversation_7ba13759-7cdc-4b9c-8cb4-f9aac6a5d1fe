apiVersion: v1
kind: Secret
metadata:
  name: oci-backup-credentials
  namespace: owncloud
type: Opaque
stringData:
  rclone.conf: |
    [oci]
    type = s3
    provider = Other
    env_auth = false
    access_key_id = <replace with your OCI user OCID>
    secret_access_key = <replace with your OCI user API key>
    endpoint = https://objectstorage.sa-saopaulo-1.oraclecloud.com
    region = sa-saopaulo-1
    force_path_style = true
    location_constraint = sa-saopaulo-1
    acl = private
