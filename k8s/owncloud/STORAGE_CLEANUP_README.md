# OwnCloud Storage Cleanup - Sistema de Monitoramento e Limpeza Automática

Este sistema monitora o espaço disponível no volume `owncloud-storage` e executa limpeza automática quando necessário.

## 📋 Funcionalidades

- **Monitoramento diário**: Verifica o espaço disponível no volume todos os dias às 02:00
- **Limpeza automática**: Remove arquivos mais antigos quando o espaço livre for menor que 15%
- **Notificações**: Envia resumos e alertas para Google Chat via webhook
- **Logs detalhados**: Registra todas as operações para auditoria
- **Segurança**: Limita a 1000 arquivos por execução para evitar sobrecarga

## 🚀 Instalação

### 1. Aplicar os recursos no Kubernetes

```bash
# Aplicar o ConfigMap com o script
kubectl apply -f k8s/owncloud/storage-cleanup-configmap.yaml

# Aplicar o CronJob
kubectl apply -f k8s/owncloud/storage-cleanup-cronjob.yaml
```

### 2. Verificar se foi aplicado corretamente

```bash
# Verificar o ConfigMap
kubectl get configmap storage-cleanup-script -n owncloud

# Verificar o CronJob
kubectl get cronjob owncloud-storage-cleanup -n owncloud

# Ver detalhes do CronJob
kubectl describe cronjob owncloud-storage-cleanup -n owncloud
```

## 🔧 Configuração

### Parâmetros principais (no script):

- **MIN_FREE_PERCENT**: 15% (limite mínimo de espaço livre)
- **target_free_percent**: 20% (meta após limpeza)
- **FILES_PATH**: `/mnt/data/files/postoaki/files` (diretório a ser limpo)
- **Schedule**: `0 2 * * *` (execução diária às 02:00)

### Webhook do Google Chat:
O webhook está configurado no script. Para alterar:
1. Edite o ConfigMap
2. Aplique novamente: `kubectl apply -f storage-cleanup-configmap.yaml`

## 📊 Monitoramento

### Verificar execuções do CronJob:

```bash
# Listar jobs executados
kubectl get jobs -n owncloud | grep storage-cleanup

# Ver logs da última execução
kubectl logs -n owncloud job/$(kubectl get jobs -n owncloud -o name | grep storage-cleanup | tail -1 | cut -d'/' -f2)

# Ver logs em tempo real (se estiver executando)
kubectl logs -n owncloud -f job/$(kubectl get jobs -n owncloud -o name | grep storage-cleanup | tail -1 | cut -d'/' -f2)
```

### Verificar próximas execuções:

```bash
kubectl get cronjob owncloud-storage-cleanup -n owncloud -o wide
```

### Executar manualmente (para teste):

```bash
# Criar um job manual baseado no CronJob
kubectl create job --from=cronjob/owncloud-storage-cleanup manual-cleanup-test -n owncloud

# Acompanhar execução
kubectl logs -n owncloud -f job/manual-cleanup-test
```

## 📱 Notificações Google Chat

O sistema envia notificações em diferentes situações:

### 🟢 Status Normal (segundas-feiras):
- Confirma que o volume está saudável
- Mostra espaço livre atual

### 🟡 Alerta de Limpeza:
- Quando espaço livre < 15%
- Notifica início da limpeza automática

### 🧹 Resumo da Limpeza:
- Quantidade de arquivos removidos
- Espaço liberado
- Status final do volume
- Lista dos arquivos removidos (primeiros da lista)

### ❌ Erros:
- Problemas de acesso ao volume
- Falhas na execução

## 🛠️ Manutenção

### Alterar horário de execução:

1. Edite o arquivo `storage-cleanup-cronjob.yaml`
2. Modifique o campo `schedule` (formato cron)
3. Aplique: `kubectl apply -f storage-cleanup-cronjob.yaml`

### Alterar limite de espaço:

1. Edite o ConfigMap: `kubectl edit configmap storage-cleanup-script -n owncloud`
2. Modifique `MIN_FREE_PERCENT` no script
3. Salve e saia

### Pausar execuções:

```bash
# Suspender o CronJob
kubectl patch cronjob owncloud-storage-cleanup -n owncloud -p '{"spec":{"suspend":true}}'

# Reativar o CronJob
kubectl patch cronjob owncloud-storage-cleanup -n owncloud -p '{"spec":{"suspend":false}}'
```

### Remover o sistema:

```bash
kubectl delete cronjob owncloud-storage-cleanup -n owncloud
kubectl delete configmap storage-cleanup-script -n owncloud
kubectl delete serviceaccount owncloud-storage-cleanup -n owncloud
kubectl delete role owncloud-storage-cleanup -n owncloud
kubectl delete rolebinding owncloud-storage-cleanup -n owncloud
```

## 🔍 Troubleshooting

### Problema: CronJob não executa
- Verificar se o namespace está correto
- Verificar se o PVC existe e está montado
- Verificar logs do kube-scheduler

### Problema: Script falha
- Verificar se o diretório `/mnt/data/files/postoaki/files` existe
- Verificar permissões de escrita no volume
- Verificar conectividade com Google Chat webhook

### Problema: Não recebe notificações
- Testar webhook manualmente com curl
- Verificar se o webhook URL está correto
- Verificar conectividade de rede do cluster

## 📝 Logs

Os logs são armazenados em:
- **Container**: `/var/log/owncloud-cleanup.log`
- **Kubernetes**: `kubectl logs` dos jobs

Para acessar logs históricos, use:
```bash
kubectl logs -n owncloud job/owncloud-storage-cleanup-[timestamp]
```

## ⚠️ Considerações Importantes

1. **Backup**: Certifique-se de ter backups antes de ativar a limpeza automática
2. **Teste**: Execute manualmente primeiro para verificar comportamento
3. **Monitoramento**: Acompanhe as primeiras execuções via Google Chat
4. **Permissões**: O script precisa de permissão de escrita no volume
5. **Recursos**: O job usa recursos mínimos, mas monitore se necessário

## 🔄 Histórico de Versões

- **v1.0**: Versão inicial com monitoramento e limpeza automática
- Configuração de webhook Google Chat
- Logs detalhados e notificações
- Execução diária via CronJob
