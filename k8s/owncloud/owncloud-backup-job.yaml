apiVersion: batch/v1
kind: Job
metadata:
  name: owncloud-backup
  namespace: owncloud
spec:
  template:
    spec:
      containers:
      - name: rclone
        image: rclone/rclone:latest
        command:
        - "/bin/sh"
        - "-c"
        - |
          mkdir -p /root/.config/rclone/
          cp /rclone-config/rclone.conf /root/.config/rclone/
          echo "Starting backup..."
          rclone sync /mnt/data oci:owncloud-archive-storage \
            --transfers 4 \
            --checkers 8 \
            --s3-upload-concurrency 4 \
            --s3-chunk-size 64M \
            --progress \
            --stats 30s \
            -v
        volumeMounts:
        - name: owncloud-data
          mountPath: /mnt/data
          subPath: files/postoaki/files
        - name: rclone-config
          mountPath: /rclone-config
          readOnly: true
      volumes:
      - name: owncloud-data
        persistentVolumeClaim:
          claimName: owncloud-pvc
      - name: rclone-config
        secret:
          secretName: rclone-config
      restartPolicy: OnFailure
