apiVersion: v1
kind: PersistentVolume
metadata:
  name: mariadb-pv
  namespace: owncloud
spec:
  capacity:
    storage: 10Gi
  accessModes:
    - ReadWriteMany
  nfs:
    path: "/FileSystem-Owncloud-MariaDB"
    server: 192.168.202.92
  persistentVolumeReclaimPolicy: Retain
  storageClassName: "nfs-storage"
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: mariadb-pvc
  namespace: owncloud
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 10Gi
  storageClassName: "nfs-storage"
---
apiVersion: v1
kind: PersistentVolume
metadata:
  name: redis-pv
  namespace: owncloud
spec:
  capacity:
    storage: 5Gi
  accessModes:
    - ReadWriteMany
  nfs:
    path: "/FileSystem-Owncloud-Redis"
    server: 192.168.202.92
  persistentVolumeReclaimPolicy: Retain
  storageClassName: "nfs-storage"
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: redis-pvc
  namespace: owncloud
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 5Gi
  storageClassName: "nfs-storage"
