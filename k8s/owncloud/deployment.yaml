apiVersion: apps/v1
kind: Deployment
metadata:
  name: owncloud
  namespace: owncloud
spec:
  replicas: 1
  selector:
    matchLabels:
      app: owncloud
  template:
    metadata:
      labels:
        app: owncloud
    spec:
      containers:
      - name: owncloud
        image: owncloud/server:latest
        ports:
        - containerPort: 8080
        env:
        - name: TZ
          value: "America/Sao_Paulo"
        - name: OWNCLOUD_DOMAIN
          value: "drive2.postoaki.com.br,drive2.postoaki.app"
        - name: OWNCLOUD_TRUSTED_DOMAINS
          value: "drive2.postoaki.com.br,drive2.postoaki.app,owncloud.owncloud.svc.cluster.local"
        - name: OWNCLOUD_DB_TYPE
          value: "mysql"
        - name: OWNCLOUD_DB_NAME
          value: "owncloud"
        - name: OWNCLOUD_DB_USERNAME
          value: "usr_owncloud"
        - name: OWNCLOUD_DB_PASSWORD
          valueFrom:
              secretKeyRef:
                name: mariadb-secrets
                key: mysql-user-password
        - name: OWNCLOUD_DB_HOST
          value: "mariadb.owncloud.svc.cluster.local"
        - name: OWNCLOUD_ADMIN_USERNAME
          value: "postoaki"
        - name: OWNCLOUD_ADMIN_PASSWORD
          valueFrom:
              secretKeyRef:
                name: owncloud-admin-secret
                key: admin-password
        - name: OWNCLOUD_MYSQL_UTF8MB4
          value: "true"
        - name: OWNCLOUD_REDIS_ENABLED
          value: "true"
        - name: OWNCLOUD_REDIS_HOST
          value: "redis.owncloud.svc.cluster.local"
        livenessProbe:
          exec:
            command: ["/usr/bin/healthcheck"]
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 5
        readinessProbe:
          exec:
            command: ["/usr/bin/healthcheck"]
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 5
        resources:
          requests:
            memory: "400Mi"
            cpu: "500m"
          limits:
            memory: "800Mi"
            cpu: "1000m"
        volumeMounts:
        - name: owncloud-storage
          mountPath: /mnt/data
      volumes:
      - name: owncloud-storage
        persistentVolumeClaim:
          claimName: owncloud-pvc-standard
