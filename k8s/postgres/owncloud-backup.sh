#!/bin/sh
set -e

#BASE_URL="http://owncloud.owncloud.svc.cluster.local:8080/remote.php/dav/files"
BASE_URL="https://drive2.postoaki.app/remote.php/dav/files"
USERNAME="postoaki"
PASSWORD="WODOP-GYUZM-MZBCB-AFIYP"
BACKUP_BASE_DIR="/postgresql/pgdumps"
CHUNK_SIZE="100M"  # Tamanho do chunk (100MB)
SENT_FILES_LOG="/tmp/owncloud_sent_files.log"

echo "Iniciando sincronização de backups PostgreSQL para OwnCloud"
echo "Diretório base: $BACKUP_BASE_DIR"

# Criar arquivo de log se não existir
touch "$SENT_FILES_LOG"

# Função para verificar se arquivo já foi enviado
is_file_sent() {
    local file_path="$1"
    grep -q "^$file_path$" "$SENT_FILES_LOG" 2>/dev/null
}

# Função para marcar arquivo como enviado
mark_file_sent() {
    local file_path="$1"
    echo "$file_path" >> "$SENT_FILES_LOG"
}

# Função para verificar se arquivo existe no OwnCloud
check_file_exists_owncloud() {
    local destination="$1"
    curl -u "$USERNAME:$PASSWORD" \
        -X PROPFIND \
        --silent \
        --fail \
        "$BASE_URL/$USERNAME$destination" >/dev/null 2>&1
}

# Função para criar diretório no OwnCloud se não existir
create_owncloud_directory() {
    local dir_path="$1"
    curl -u "$USERNAME:$PASSWORD" \
        -X MKCOL \
        --silent \
        "$BASE_URL/$USERNAME$dir_path" 2>/dev/null || true
}

# Função para upload chunked mais eficiente
upload_chunked() {
    local file="$1"
    local destination="$2"
    local file_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file" 2>/dev/null)
    local chunk_size_bytes=$((500 * 1024 * 1024))  # 500MB em bytes para arquivos grandes
    local total_chunks=$(( (file_size + chunk_size_bytes - 1) / chunk_size_bytes ))
    local chunk_num=0
    local temp_chunk="/tmp/chunk_$$"

    echo "Tamanho do arquivo: $file_size bytes ($(echo "scale=2; $file_size/1024/1024/1024" | bc -l)GB)"
    echo "Total de chunks: $total_chunks (chunks de 500MB)"

    # Upload cada chunk
    while [ $chunk_num -lt $total_chunks ]; do
        local start_byte=$((chunk_num * chunk_size_bytes))
        local end_byte=$((start_byte + chunk_size_bytes - 1))

        # Ajustar o último chunk
        if [ $end_byte -ge $file_size ]; then
            end_byte=$((file_size - 1))
        fi

        local current_chunk_size=$((end_byte - start_byte + 1))

        echo "Enviando chunk $((chunk_num + 1))/$total_chunks (bytes $start_byte-$end_byte, $(echo "scale=1; $current_chunk_size/1024/1024" | bc -l)MB)"

        # Criar chunk temporário usando dd com block size maior para eficiência
        dd if="$file" of="$temp_chunk" bs=1M skip=$((start_byte / 1024 / 1024)) count=$((current_chunk_size / 1024 / 1024 + 1)) 2>/dev/null

        # Ajustar tamanho exato do chunk se necessário
        if [ -f "$temp_chunk" ]; then
            local actual_size=$(stat -f%z "$temp_chunk" 2>/dev/null || stat -c%s "$temp_chunk" 2>/dev/null)
            if [ $actual_size -gt $current_chunk_size ]; then
                dd if="$temp_chunk" of="${temp_chunk}.exact" bs=1 count=$current_chunk_size 2>/dev/null
                mv "${temp_chunk}.exact" "$temp_chunk"
            fi
        fi

        # Upload do chunk usando -T para não carregar na memória
        curl -u "$USERNAME:$PASSWORD" \
            -T "$temp_chunk" \
            -H "Content-Type: application/octet-stream" \
            -H "Content-Range: bytes $start_byte-$end_byte/$file_size" \
            -X PUT \
            --max-time 3600 \
            --retry 3 \
            --retry-delay 5 \
            --connect-timeout 60 \
            "$BASE_URL/$USERNAME$destination"

        local upload_result=$?

        # Limpar chunk temporário
        rm -f "$temp_chunk" "${temp_chunk}.exact" 2>/dev/null

        if [ $upload_result -ne 0 ]; then
            echo "Erro ao enviar chunk $((chunk_num + 1))"
            return 1
        fi

        chunk_num=$((chunk_num + 1))

        # Pequena pausa entre chunks para não sobrecarregar
        sleep 2
    done

    return 0
}

# Alternativa: Upload com streaming simples usando -T (não carrega na memória)
upload_streaming() {
    local file="$1"
    local destination="$2"

    echo "Fazendo upload streaming do arquivo..."

    # Upload com -T (PUT direto do arquivo, sem carregar na memória)
    curl -u "$USERNAME:$PASSWORD" \
        -T "$file" \
        -H "Content-Type: application/octet-stream" \
        --max-time 7200 \
        --retry 3 \
        --retry-delay 10 \
        --retry-max-time 21600 \
        --connect-timeout 60 \
        --keepalive-time 60 \
        -v \
        "$BASE_URL/$USERNAME$destination"

    return $?
}

# Função para processar um arquivo de backup
process_backup_file() {
    local file_path="$1"
    local relative_path="$2"  # Caminho relativo a partir de /postgresql/pgdumps
    local filename=$(basename "$file_path")
    local destination="/banco-producao/$relative_path/$filename"
    local dir_destination="/banco-producao/$relative_path"

    echo "Processando: $file_path"
    echo "Destino: $destination"

    # Verificar se já foi enviado
    if is_file_sent "$file_path"; then
        echo "Arquivo já foi enviado anteriormente. Pulando..."
        return 0
    fi

    # Verificar se existe no OwnCloud
    if check_file_exists_owncloud "$destination"; then
        echo "Arquivo já existe no OwnCloud. Marcando como enviado..."
        mark_file_sent "$file_path"
        return 0
    fi

    # Criar diretório no OwnCloud se necessário
    create_owncloud_directory "$dir_destination"

    # Verificar tamanho do arquivo para escolher método
    local file_size=$(stat -f%z "$file_path" 2>/dev/null || stat -c%s "$file_path" 2>/dev/null)
    local file_size_gb=$(echo "scale=2; $file_size/1024/1024/1024" | bc -l 2>/dev/null || echo "0")

    echo "Tamanho do arquivo: $file_size bytes (${file_size_gb}GB)"

    # Para arquivos menores que 1GB, tentar streaming primeiro
    if [ $file_size -lt 1073741824 ]; then
        echo "Arquivo pequeno (<1GB), tentando upload streaming..."
        if upload_streaming "$file_path" "$destination"; then
            echo "Arquivo enviado com sucesso via streaming!"
            mark_file_sent "$file_path"
            return 0
        else
            echo "Upload streaming falhou, tentando upload chunked..."
        fi
    else
        echo "Arquivo grande (>=1GB), usando upload chunked diretamente..."
    fi

    # Upload chunked para arquivos grandes ou quando streaming falha
    if upload_chunked "$file_path" "$destination"; then
        echo "Arquivo enviado com sucesso via chunks!"
        mark_file_sent "$file_path"
        return 0
    else
        echo "Erro: Falha no upload chunked para $file_path"
        return 1
    fi
}

# Função principal para processar todos os backups
process_all_backups() {
    local total_files=0
    local processed_files=0
    local skipped_files=0
    local failed_files=0

    echo "Procurando arquivos de backup em $BACKUP_BASE_DIR..."

    # Encontrar todos os arquivos de backup (recursivamente)
    find "$BACKUP_BASE_DIR" -type f \( -name "*.pgr" -o -name "*.sql" -o -name "*.log" \) | while read -r file_path; do
        total_files=$((total_files + 1))

        # Calcular caminho relativo
        relative_path=$(dirname "$file_path" | sed "s|^$BACKUP_BASE_DIR/||")

        echo "----------------------------------------"
        echo "Arquivo $total_files: $file_path"

        if process_backup_file "$file_path" "$relative_path"; then
            processed_files=$((processed_files + 1))
            echo "✓ Sucesso"
        else
            failed_files=$((failed_files + 1))
            echo "✗ Falha"
        fi

        # Pequena pausa entre arquivos
        sleep 2
    done

    echo "========================================="
    echo "Resumo da sincronização:"
    echo "Total de arquivos encontrados: $total_files"
    echo "Arquivos processados com sucesso: $processed_files"
    echo "Arquivos que falharam: $failed_files"
    echo "========================================="
}

# Verificar se o diretório de backup existe
if [ ! -d "$BACKUP_BASE_DIR" ]; then
    echo "Erro: Diretório de backup não encontrado: $BACKUP_BASE_DIR"
    exit 1
fi

# Criar diretório base no OwnCloud
create_owncloud_directory "/banco-producao"

# Executar processamento
process_all_backups
