apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgres-pvc
  namespace: postgres
  labels:
    app: postgres
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 50Gi
---
apiVersion: v1
kind: PersistentVolume
metadata:
  name: postgres-storage-pv
  namespace: postgres
  labels:
    app: shared-storage
spec:
  capacity:
    storage: 50Gi
  accessModes:
    - ReadWriteMany
  nfs:
    path: "/FileSystem-Postgres"
    server: 192.168.202.92
  persistentVolumeReclaimPolicy: Retain
  storageClassName: "nfs-storage"
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgres-storage-pvc
  namespace: postgres
  labels:
    app: shared-storage
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 50Gi
  storageClassName: "nfs-storage"
