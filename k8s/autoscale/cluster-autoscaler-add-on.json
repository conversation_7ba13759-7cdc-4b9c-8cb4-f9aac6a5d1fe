{"addonName": "ClusterAutoscaler", "configurations": [{"key": "nodes", "value": "1:5:ocid1.nodepool.oc1.sa-saopaulo-1.aaaaaaaaunnqihwqz4wkpeok3j4vcwwlfuhurdm5r6b3imdxin6gdirmvk2a"}, {"key": "authType", "value": "workload"}, {"key": "numOfReplicas", "value": "1"}, {"key": "maxNodeProvisionTime", "value": "15m"}, {"key": "scaleDownDelayAfterAdd", "value": "15m"}, {"key": "scaleDownUnneededTime", "value": "10m"}, {"key": "annotations", "value": "{\"prometheus.io/scrape\":\"true\",\"prometheus.io/port\":\"8086\"}"}]}