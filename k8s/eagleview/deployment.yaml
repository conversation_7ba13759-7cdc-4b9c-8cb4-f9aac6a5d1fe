apiVersion: apps/v1
kind: Deployment
metadata:
  name: eagleview
  namespace: eagleview
spec:
  replicas: 1
  selector:
    matchLabels:
      app: eagleview
  template:
    metadata:
      labels:
        app: eagleview
    spec:
      containers:
        - name: eagleview
          image: gru.ocir.io/grbvypj1mx3p/eagleview:20250611152227
          imagePullPolicy: Always
          ports:
            - containerPort: 8080
          env:
            - name: TZ
              value: "America/Sao_Paulo"
            - name: APP_OPTS
              value: "-Dambie<PERSON>=PRODUCAO -Ddatabase.banco=eagle -Dusuario.banco=usr_eagle -Dsenha.banco='ba(vg8Mg5Pxr!2CnWu_5qTC)' -Dip-porta.banco='***************:5000' -Dportal.secret_key_jwt=AA3S21D65CV419H4RB4R9UN4Y9B4VRT4RB98T4NT1YN9841T9G84YUBYU -Dportal.secret_criptografia=PRODUCAO"
          resources:
            limits:
              memory: "1Gi"
              cpu: "1"
          # livenessProbe:
          #   httpGet:
          #     path: /health
          #     port: 8080
          #   initialDelaySeconds: 60
          #   periodSeconds: 10
          # readinessProbe:
          #   httpGet:
          #     path: /health
          #     port: 8080
          #   initialDelaySeconds: 60
          #   periodSeconds: 10
      imagePullSecrets:
        - name: docker-registry