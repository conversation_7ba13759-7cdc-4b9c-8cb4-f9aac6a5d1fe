const fs = require('fs');
const csv = require('csv-parser');
const axios = require('axios');
const dns2 = require('dns2');
const yargs = require('yargs');
const cliProgress = require('cli-progress');
const resolver = new dns2();

const ZONE_ID = '7610505407472c3c3ab31830c68e8452';
const CLOUDFLARE_API_TOKEN = '****************************************';
const NEW_DNS_VALUE = 'lb01.postoaki.com';
const OLD_DNS_VALUE = 'www.postoaki.com';

async function readCSV(filePath) {
  return new Promise((resolve, reject) => {
    const records = [];
    fs.createReadStream(filePath)
      .pipe(csv())
      .on('data', (data) => records.push(data))
      .on('end', () => resolve(records))
      .on('error', (error) => reject(error));
  });
}

function formatDatePtBr(date) {
  return new Intl.DateTimeFormat('pt-BR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  }).format(date);
}

async function verifyDNSAndHealth(record) {
  let resolved = false;
  const results = {dns: {success: [], failed: []}, healthcheck: {success: [], failed: []}};
  while (!resolved) {
    const addresses = await resolver.resolveA(record['DNS']);
    if (addresses.answers.some(answer => answer.name === OLD_DNS_VALUE)) {
      results.dns.failed.push('Não resolvido para ' +  record['DNS'] + ' em '+ formatDatePtBr(new Date()))
      await new Promise(resolve => setTimeout(resolve, 5000));
    } else {
      resolved = true;
      results.dns.success.push('DNS resolvido com sucesso para ' + record['DNS']  + ' em '+ formatDatePtBr(new Date()));
    }
  }

  const url = record['DNS'];
  if (url) {
    const urlHttp = `http://${url}/health`;
    const response = await axios.get(urlHttp);
    if(response.status === 200) results.healthcheck.success.push('Health check http realizado com sucesso para ' + urlHttp);

    if (response.status !== 200) {
      results.healthcheck.failed.push('Health check http falhou para ' + urlHttp);
      process.exit(1);
    }
    
    const urlHttps = `https://${url}/health`;
    const responsehttps = await axios.get(urlHttps);
    if(responsehttps.status === 200) results.healthcheck.success.push('Health check https realizado com sucesso para ' + urlHttps);

    if (responsehttps.status !== 200){
      results.healthcheck.failed.push('Health check https falhou para ' + urlHttps);
      process.exit(1);
    }

    return results;
  }
  
}

async function getAllDNSRecords() {
  let page = 1;
  const perPage = 100;
  let allRecords = [];
  let hasMore = true;

  while (hasMore) {
    const response = await axios.get(`https://api.cloudflare.com/client/v4/zones/${ZONE_ID}/dns_records`, {
      headers: {
        'Authorization': `Bearer ${CLOUDFLARE_API_TOKEN}`,
        'Content-Type': 'application/json'
      },
      params: {
        page: page,
        per_page: perPage
      }
    });

    const records = response.data.result;
    allRecords = allRecords.concat(records);

    if (response.data.result_info.page < response.data.result_info.total_pages) {
      page++;
    } else {
      hasMore = false;
    }
  }

  return allRecords;
}

async function updateCloudflareDNSRecord(record) {
  let result = { success: true, message: "" };
  try {
    const dnsRecords = await getAllDNSRecords();
    const existingRecord = dnsRecords.find(r => r.name === record['DNS']);

    if (existingRecord) {
      const updatedRecord = {
        type: existingRecord.type,
        name: record['DNS'],
        content: NEW_DNS_VALUE, // Novo valor do DNS
        proxied: true, // Habilita o proxy do Cloudflare
      };

      // Atualizar registro DNS
      await axios.put(`https://api.cloudflare.com/client/v4/zones/${ZONE_ID}/dns_records/${existingRecord.id}`, updatedRecord, {
        headers: {
          'Authorization': `Bearer ${CLOUDFLARE_API_TOKEN}`,
          'Content-Type': 'application/json'
        }
      });

      result.message = `Registro DNS para ${record['DNS']} atualizado com sucesso para ${NEW_DNS_VALUE}`;
    } else {
      result.success = false;
      result.message = `Registro DNS para ${record['DNS']} não encontrado no Cloudflare`;
    }
  } catch (error) {
    result.success = false;
    result.message = `Erro ao atualizar o registro DNS no Cloudflare: ${error.message}`;
  }

  return result;
}


async function updateCSV(records, filePath) {
  const csvWriter = require('csv-writer').createObjectCsvWriter({
    path: filePath,
    header: Object.keys(records[0]).map(key => ({ id: key, title: key }))
  });

  await csvWriter.writeRecords(records);
}
(async () => {
  try {
    const argv = yargs
    .option('check-all', {
      alias: 'c',
      description: 'Verifica o helath check de todos os registros DNS',
      type: 'boolean',
      default: false,
      demandOption: false
    })
    .help()
    .alias('help', 'h')
    .argv;

    const filePath = 'dns.csv';
    const records = await readCSV(filePath);


    let checkCount = 0;
    let migrationClount = 0;
    let migratedCount = 0;
    for (let i = 0; i < records.length; i++) {
      const record = records[i];
      if(record['Migra para Kubernetes'] === 'Sim' && argv['check-all'] === true){
        checkCount++;
      }

      if (record['Migra para Kubernetes'] === 'Migrando') {
        migrationClount++;
      }

      if (record['Migra para Kubernetes'] === 'Sim') {
        migratedCount++;
      }
    }

    const progressBarTotalCount = checkCount + migrationClount;
    const progressBar = new cliProgress.SingleBar({}, cliProgress.Presets.shades_classic);
    progressBar.start(progressBarTotalCount, 0);
    let progressBarCount = 0;

    const results = {dns: {success: [], failed: []}, healthcheck: {success: [], failed: []}};

    for (let i = 0; i < records.length; i++) {
      const record = records[i];
      if (record['Migra para Kubernetes'] === 'Migrando') {
        const addresses = await resolver.resolveA(record['DNS']);
        const oldDnsRecord = addresses.answers.find(answer => answer.name === OLD_DNS_VALUE);
        if (oldDnsRecord) {
          const resultCloudFlare = await updateCloudflareDNSRecord(record);
          const recordResults = await verifyDNSAndHealth(record);
          results.dns.success.push(...recordResults.dns.success);
          results.dns.failed.push(...recordResults.dns.failed);
          results.healthcheck.success.push(...recordResults.healthcheck.success);
          results.healthcheck.failed.push(...recordResults.healthcheck.failed);
          if(resultCloudFlare.success) results.dns.success.push(resultCloudFlare.message);
          else results.dns.failed.push(resultCloudFlare.message);

          record['Migra para Kubernetes'] = 'Sim';
          await updateCSV(records, filePath);

          migratedCount++;

          progressBarCount++;
          progressBar.update(progressBarCount);
          
        }else{
          const recordResults = await verifyDNSAndHealth(record);
          results.dns.success.push(...recordResults.dns.success);
          results.dns.failed.push(...recordResults.dns.failed);
          results.healthcheck.success.push(...recordResults.healthcheck.success);
          results.healthcheck.failed.push(...recordResults.healthcheck.failed);

          record['Migra para Kubernetes'] = 'Sim';
          await updateCSV(records, filePath);
        }
      }else if(argv['check-all'] === true && record['Migra para Kubernetes'] === 'Sim'){
        const recordResults = await verifyDNSAndHealth(record, false);
        results.dns.success.push(...recordResults.dns.success);
        results.dns.failed.push(...recordResults.dns.failed);
        results.healthcheck.success.push(...recordResults.healthcheck.success);
        results.healthcheck.failed.push(...recordResults.healthcheck.failed);


        progressBar.update(i + 1);
      }
    }
    progressBar.stop();
    console.log('Resultados:', results);
    console.log(`Migração concluída com sucesso. ${migrationClount} registros migrados e testados agora.`);
    console.log('Total de registros migrados:', migratedCount);
    process.exit(0);
  } catch (error) {
    console.error('Erro durante a migração:', error);
    process.exit(1);
  }
})();