const fs = require('fs');
const csv = require('csv-parser');
const axios = require('axios');
const yargs = require('yargs');
const path = require('path');

const ZONE_ID = '7610505407472c3c3ab31830c68e8452';
const CLOUDFLARE_API_TOKEN = '****************************************';

// Ler registros do arquivo CSV
async function readCSV(filePath) {
  return new Promise((resolve, reject) => {
    const records = [];
    fs.createReadStream(filePath)
      .pipe(csv())
      .on('data', (data) => records.push(data))
      .on('end', () => resolve(records))
      .on('error', (error) => reject(error));
  });
}

async function getAllDNSRecords() {
  let page = 1;
  const perPage = 100;
  let allRecords = [];
  let hasMore = true;

  while (hasMore) {
    const response = await axios.get(`https://api.cloudflare.com/client/v4/zones/${ZONE_ID}/dns_records`, {
      headers: {
        'Authorization': `Bearer ${CLOUDFLARE_API_TOKEN}`,
        'Content-Type': 'application/json'
      },
      params: {
        page: page,
        per_page: perPage
      }
    });

    const records = response.data.result;
    allRecords = allRecords.concat(records);

    if (response.data.result_info.page < response.data.result_info.total_pages) {
      page++;
    } else {
      hasMore = false;
    }
  }

  return allRecords;
}

// Script principal
(async () => {
  const argv = yargs
    .option('app', {
      alias: 'app',
      description: 'Tipo de plicação que o dns pertence',
      type: 'string',
      choices: ['api', 'infra', 'web'],
      demandOption: true
    })
    .help()
    .alias('help', 'h')
    .argv;

  try {
    const filePath = path.resolve(__dirname, 'dns.csv');
    const csvRecords = await readCSV(filePath);
    const cloudflareRecords = await getAllDNSRecords();

    const csvDomains = csvRecords.map(record => record['DNS']);
    let cloudflareDomains = cloudflareRecords.map(record => record.name);
    
    const infraDomains = cloudflareDomains.filter(domain => domain.includes('*.api.postoaki.com') || domain.includes('_acme-challenge'));

    if(argv.app === 'api') {
      cloudflareDomains = cloudflareDomains.filter(domain => domain.includes('.api.'));
      cloudflareDomains = cloudflareDomains.filter(domain => !infraDomains.includes(domain));
    }

    const domainsNotInCSV = cloudflareDomains.filter(domain => !csvDomains.includes(domain));

    console.log('Domínios no Cloudflare, mas não na planilha dns.csv:');
    domainsNotInCSV.forEach(domain => console.log(domain));
  } catch (error) {
    console.error('Erro durante a execução do script:', error);
    process.exit(1);
  }
})();