version: "3"

services:
  nginx:
    container_name: nginx
    restart: always
    build:
      context: ./nginx
      dockerfile: Dockerfile
    volumes:
      - /opt/server/nginx/htdocs:/var/www/html
      - /opt/server/nginx/log:/var/log/nginx
      - /opt/server/nginx/sites/:/etc/nginx/conf.d
    ports:
      - "80:80"
      - "443:443"
    links:
      - owncloud
  owncloud:
    image: owncloud/server:latest
    container_name: owncloud
    restart: always
    ports:
      - 8000:8080
    depends_on:
      - mariadb
      - redis
    environment:
      - OWNCLOUD_DOMAIN=drive2.postoaki.com.br
      - OWNCLOUD_TRUSTED_DOMAINS=drive2.postoaki.com.br
      - OWNCLOUD_DB_TYPE=mysql
      - OWNCLOUD_DB_NAME=owncloud
      - OWNCLOUD_DB_USERNAME=usr_owncloud
      - OWNCLOUD_DB_PASSWORD=6PwBO6odIgLKYC1OBj4OjC2OWiuaq0o7jzs3CB7z
      - OWNCLOUD_DB_HOST=mariadb
      - OWNCLOUD_ADMIN_USERNAME=postoaki
      - OWNCLOUD_ADMIN_PASSWORD=postoakidrive321456
      - OWNCLOUD_MYSQL_UTF8MB4=true
      - OWNCLOUD_REDIS_ENABLED=true
      - OWNCLOUD_REDIS_HOST=redis
    healthcheck:
      test: ["CMD", "/usr/bin/healthcheck"]
      interval: 30s
      timeout: 10s
      retries: 5
    volumes:
      - /opt/server/owncloud:/mnt/data
  mariadb:
    image: mariadb:10
    container_name: owncloud_mariadb
    restart: always
    environment:
      - MYSQL_ROOT_PASSWORD=6PwBO6odIgLKYC1OBj4OjC2OWiuaq0o7jzs3CB7z
      - MYSQL_USER=usr_owncloud
      - MYSQL_PASSWORD=6PwBO6odIgLKYC1OBj4OjC2OWiuaq0o7jzs3CB7z
      - MYSQL_DATABASE=owncloud
    command: ["--max-allowed-packet=128M", "--innodb-log-file-size=64M"]
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-u", "root", "--password=owncloud"]
      interval: 10s
      timeout: 5s
      retries: 5
    volumes:
      - /opt/server/owncloud_mariadb:/var/lib/mysql
  redis:
    image: redis:6
    container_name: owncloud_redis
    restart: always
    command: ["--databases", "1"]
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    volumes:
      - /opt/server/owncloud_redis:/data