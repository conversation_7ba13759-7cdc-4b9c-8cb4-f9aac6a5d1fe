version: "3"
services:
  nginx:
    container_name: nginx
    restart: always
    build:
      context: ./nginx
      dockerfile: Dockerfile
    volumes:
      - /opt/server/nginx/htdocs:/var/www/html
      - /opt/server/nginx/log:/var/log/nginx
      - /opt/server/nginx/sites/:/etc/nginx/conf.d
    ports:
      - "80:80"
      - "443:443"
    links:
      - tomcat-homologacao
      - tomcat-gameficacao
      - tomcat-master
  tomcat-homologacao:
    container_name: tomcat-homologacao
    image: tomcat:8.5-jdk17
    build:
      context: ./web
      dockerfile: Dockerfile
    restart: always
    ports:
      - "8080:8080"
    volumes:
      - /opt/server/tomcat-homologacao/webapps/:/usr/local/tomcat/webapps
    environment:
      TZ: "America/Sao_Paulo"
      JAVA_OPTS: -server -Xms4096M -Xms8096M -Xmn200M   -Dsun.jnu.encoding=UTF8 -Dfile.encoding=UTF8 -Dfile.encoding.pkg=sun.io -Dsun.io.unicode.encoding=UnicodeLittle -Duser.country=BR -Duser.language=pt -Duser.timezone=GMT-0300 -Ddebug=false -Dpostoaki.email=true -Dpostoaki.enviaremailerro=true -Dpostoaki.enviarsms=true -Dpostoaki.hostname=http://apihomolog.postoaki.com.br -Dpostoaki.integracao.cerrado=false -Dpostoaki.servico.awssns=true -Dspring.datasource.password=BIXlmXa7g1Lv8uVNc54fxSKhqSH8svS3 -Dspring.datasource.url=******************************************************* -Dspring.datasource.username=postgres -Dspring.jpa.hibernate.ddl-auto=none -Dspring.jpa.show-sql=false -Duser.timezone=Americas/Sao_Paulo -Dpostoaki.jsonlog=true -Dpostoaki.schedules=false -Dlog4j.configurationFile=log4j2.xml -Dpostoaki.enviarpush=false -Dspring.datasource.hikari.maximum-pool-size=30 -Dpostoaki.metabase.url=https://bi.postoaki.com.br -Dpostoaki.metabase.secret_key=97431c05d02b6d37834593d75c72c5b75d57beca7992474da93f35b665bc875e -Dpostoaki.integration.mercafacil.base_url=https://api.mercafacil.com/v1/ -Dpostoaki.integration.mercafacil.base_url=https://api.mercafacil.com/v1/ -Dpostoaki.integration.mercafacil.api_key -Dspring.profiles.active=dev -Dpostoaki.servicopreco=false
    depends_on:
      - srv-postgres
    links:
      - srv-postgres
  tomcat-gameficacao:
    container_name: tomcat-gameficacao
    image: tomcat:9-jdk21-temurin
    build: ./web
    restart: always
    ports:
      - "8181:8080"
    volumes:
      - /opt/server/tomcat-gameficacao/webapps/:/usr/local/tomcat/webapps
    environment:
      TZ: "America/Sao_Paulo"
      JAVA_OPTS: -server -Xms4096M -Xms8096M -Xmn200M -Dsun.jnu.encoding=UTF8 -Dfile.encoding=UTF8 -Dfile.encoding.pkg=sun.io -Dsun.io.unicode.encoding=UnicodeLittle -Duser.country=BR -Duser.language=pt -Duser.timezone=GMT-0300 -Ddebug=false -Dpostoaki.email=true -Dpostoaki.enviaremailerro=true -Dpostoaki.enviarsms=true -Dpostoaki.hostname=http://apigameficacao.postoaki.com.br -Dpostoaki.integracao.cerrado=false -Dpostoaki.servico.awssns=true -Dspring.datasource.password=BIXlmXa7g1Lv8uVNc54fxSKhqSH8svS3 -Dspring.datasource.url=********************************************* -Dspring.datasource.username=postgres -Dspring.jpa.hibernate.ddl-auto=none -Dspring.jpa.show-sql=false -Duser.timezone=Americas/Sao_Paulo -Dpostoaki.jsonlog=true -Dpostoaki.schedules=false -Dlog4j.configurationFile=log4j2.xml -Dpostoaki.enviarpush=false -Dspring.datasource.hikari.maximum-pool-size=30 -Dintegracao.yampi.url=url -Dintegracao.yampi.token=token -Dintegracao.yampi.secret-key=key -Dintegracao.yampi.webhook.pedido-criado.secret=secret -Dintegracao.itau.url-auth=https://sts.itau.com.br/api/oauth/token -Dintegracao.itau.url-api=https://secure.api.itau/pix_recebimentos/v2 -Dintegracao.itau.grant_type=client_credentials -Ditegracao.itau.client_id=d9ca7dfe-24da-4052-b669-59980b9de517 -Dintegracao.itau.client_secret=a6b67b99-0729-400d-a399-52a9e9eb5658 -Dintegracao.itau.certificate_password=r.HqYA8VYKe*zzzqBQCs -Dspring.profiles.active=qa -Dpostoaki.metabase.url=https://bi.postoaki.com.br -Dpostoaki.metabase.secret_key=97431c05d02b6d37834593d75c72c5b75d57beca7992474da93f35b665bc875e -Dpostoaki.integration.mercafacil.base_url=https://api.mercafacil.com/v1/ -Dpostoaki.integration.mercafacil.api_key -Dpostoaki.servicopreco=false

    depends_on:
      - pgbouncer
    links:
      - pgbouncer
  tomcat-master:
    container_name: tomcat-master
    image: tomcat:8.5-jdk17
    build: ./web
    restart: always
    ports:
      - "8282:8080"
    volumes:
      - /opt/server/tomcat-master/webapps/:/usr/local/tomcat/webapps
    environment:
      TZ: "America/Sao_Paulo"
      JAVA_OPTS: -server -Xms4096M -Xms8096M -Xmn200M -Dsun.jnu.encoding=UTF8 -Dfile.encoding=UTF8 -Dfile.encoding.pkg=sun.io -Dsun.io.unicode.encoding=UnicodeLittle -Duser.country=BR -Duser.language=pt -Duser.timezone=GMT-0300 -Ddebug=false -Dpostoaki.email=true -Dpostoaki.enviaremailerro=true -Dpostoaki.enviarsms=true -Dpostoaki.hostname=http://apimaster.postoaki.com.br -Dpostoaki.integracao.cerrado=false -Dpostoaki.servico.awssns=true -Dspring.datasource.password=BIXlmXa7g1Lv8uVNc54fxSKhqSH8svS3 -Dspring.datasource.url=************************************************** -Dspring.datasource.username=postgres -Dspring.jpa.hibernate.ddl-auto=none -Dspring.jpa.show-sql=false -Duser.timezone=Americas/Sao_Paulo -Dpostoaki.jsonlog=true -Dpostoaki.schedules=false -Dlog4j.configurationFile=log4j2.xml -Dpostoaki.enviarpush=false -Dspring.datasource.hikari.maximum-pool-size=30 -Dintegracao.yampi.secret-key=key -Dintegracao.yampi.webhook.pedido-criado.secret=secret -Dintegracao.yampi.url=http://teste.com -Dintegracao.yampi.token=99999999999999999999 -Dspringdoc.swagger-ui.enabled=true -Dspringdoc.api-docs.enabled=true -Dintegracao.itau.url-auth=https://sts.itau.com.br/api/oauth/token -Dintegracao.itau.url-api=https://secure.api.itau/pix_recebimentos/v2 -Dintegracao.itau.grant_type=client_credentials -Ditegracao.itau.client_id=d9ca7dfe-24da-4052-b669-59980b9de517 -Dintegracao.itau.client_secret=a6b67b99-0729-400d-a399-52a9e9eb5658 -Dintegracao.itau.certificate_password=r.HqYA8VYKe*zzzqBQCs -Dspring.profiles.active=qa -Dpostoaki.metabase.url=https://bi.postoaki.com.br -Dpostoaki.metabase.secret_key=97431c05d02b6d37834593d75c72c5b75d57beca7992474da93f35b665bc875e -Dpostoaki.integration.mercafacil.base_url=https://api.mercafacil.com/v1/ -Dpostoaki.integration.mercafacil.base_url=https://api.mercafacil.com/v1/ -Dpostoaki.integration.mercafacil.api_key -Dpostoaki.servicopreco=false
    depends_on:
      - srv-postgres
    links:
      - srv-postgres
  srv-postgres:
    image: postgres:13-alpine
    container_name: postgres
    command: ["postgres", "-c", "max_connections=2000"]
    environment:
      POSTGRES_PASSWORD: "BIXlmXa7g1Lv8uVNc54fxSKhqSH8svS3"
      POSTGRES_USER: "postgres"
      PGDATA: /var/lib/postgresql/data
    ports:
      - "5432:5432"
    volumes:
      - /opt/server/postgresql/data:/var/lib/postgresql/data
  sonarqube:
    image: sonarqube:latest
    container_name: sonarqube
    ports:
      - "9000:9000"
    environment:
      SONAR_JDBC_URL: "*********************************************"
      SONAR_JDBC_USERNAME: "postgres"
      SONAR_JDBC_PASSWORD: "BIXlmXa7g1Lv8uVNc54fxSKhqSH8svS3"
      SONAR_ES_BOOTSTRAP_CHECKS_DISABLE: "true"
    volumes:
      - /opt/server/sonarqube/sonarqube_data:/opt/sonarqube/data
      - /opt/server/sonarqube/sonarqube_logs:/opt/sonarqube/logs
      - /opt/server/sonarqube/sonarqube_extensions:/opt/sonarqube/extensions
    depends_on:
      - srv-postgres
  portainer:
    image: portainer/portainer-ce:latest
    container_name: portainer
    ports:
      - "9001:9000"
    volumes:
      - /opt/server/portainer/portainer_data:/data
      - /var/run/docker.sock:/var/run/docker.sock

  srv-postgres-16:
    image: postgres:16-alpine
    container_name: srv-postgres-16
    command: [ "postgres", "-c", "max_connections=2000" ]
    environment:
      POSTGRES_PASSWORD: "BIXlmXa7g1Lv8uVNc54fxSKhqSH8svS3"
      POSTGRES_USER: "postgres"
      PGDATA: /var/lib/postgresql/data16
    ports:
      - "5433:5432"
    volumes:
      - /opt/server/postgresql/data16:/var/lib/postgresql/data

  pgbouncer:
    image: edoburu/pgbouncer:latest
    container_name: pgbouncer
    ports:
      - "6432:6432"
    environment:
      POSTGRES_HOST: "${POSTGRES_HOST}"
      POSTGRES_PORT: "${POSTGRES_PORT}"
      DB_NAME: "${DB_NAME}"
      AUTH_TYPE: "${AUTH_TYPE}"
      POOL_MODE: "${POOL_MODE}"
      MAX_CLIENT_CONN: "${MAX_CLIENT_CONN}"
      DEFAULT_POOL_SIZE: "${DEFAULT_POOL_SIZE}"
    volumes:
      - type: bind
        source: ./pgbouncer/userlist.txt
        target: /etc/pgbouncer/userlist.txt

    depends_on:
      - srv-postgres-16
    entrypoint: >
      sh -c "
      if [ -z \"${POSTGRES_HOST}\" ] || [ -z \"${POSTGRES_PORT}\" ] || [ -z \"${DB_NAME}\" ]; then
        echo 'Erro: Variáveis de ambiente obrigatórias estão ausentes.';
        exit 1;
      fi;

      echo '[databases]' > /etc/pgbouncer/pgbouncer.ini;
      echo '${DB_NAME} = host=${POSTGRES_HOST} port=${POSTGRES_PORT} dbname=${DB_NAME}' >> /etc/pgbouncer/pgbouncer.ini;
      echo '' >> /etc/pgbouncer/pgbouncer.ini;
      echo '[pgbouncer]' >> /etc/pgbouncer/pgbouncer.ini;
      echo 'listen_addr = 0.0.0.0' >> /etc/pgbouncer/pgbouncer.ini;
      echo 'listen_port = 6432' >> /etc/pgbouncer/pgbouncer.ini;
      echo 'auth_type = ${AUTH_TYPE}' >> /etc/pgbouncer/pgbouncer.ini;
      echo 'auth_file = /etc/pgbouncer/userlist.txt' >> /etc/pgbouncer/pgbouncer.ini;
      echo 'pool_mode = ${POOL_MODE}' >> /etc/pgbouncer/pgbouncer.ini;
      echo 'max_client_conn = ${MAX_CLIENT_CONN}' >> /etc/pgbouncer/pgbouncer.ini;
      echo 'default_pool_size = ${DEFAULT_POOL_SIZE}' >> /etc/pgbouncer/pgbouncer.ini;
      echo 'ignore_startup_parameters = extra_float_digits' >> /etc/pgbouncer/pgbouncer.ini;

      echo 'Arquivo gerado com sucesso:';
      cat /etc/pgbouncer/pgbouncer.ini;

      echo 'Iniciando PgBouncer...';
      exec pgbouncer /etc/pgbouncer/pgbouncer.ini
      "
