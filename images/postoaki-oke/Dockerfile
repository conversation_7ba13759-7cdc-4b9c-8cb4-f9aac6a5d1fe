FROM alpine:latest

# Install dependencies
RUN apk add --no-cache bash curl python3 py3-pip openssh zip && \
    python3 -m venv /opt/venv && \
    . /opt/venv/bin/activate && \
    pip install oci-cli && \
    curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl" && \
    install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl && \
    rm kubectl

# Set timezone
RUN apk add --no-cache tzdata && \
    cp /usr/share/zoneinfo/America/Sao_Paulo /etc/localtime && \
    echo "America/Sao_Paulo" > /etc/timezone && \
    apk del tzdata

# Create necessary directories
RUN mkdir -p /root/.kube /root/.oci

# Copy configuration files
COPY kubeconfig /root/.kube/config
COPY oci_config /root/.oci/config
COPY postoaki.pem /root/.oci/postoaki.pem

# Set environment variables for OCI CLI
ENV PATH="/opt/venv/bin:$PATH"

ENTRYPOINT [ "kubectl" ]