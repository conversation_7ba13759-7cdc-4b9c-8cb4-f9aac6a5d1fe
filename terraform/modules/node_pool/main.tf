resource "oci_containerengine_node_pool" "node_pool" {
    compartment_id = var.compartment_id
    cluster_id     = var.cluster_id
    name           = "node-pool-1"
    node_shape     = "VM.Standard.E5.Flex"
    node_shape_config {
        ocpus = 2
        memory_in_gbs = 4
    }
    kubernetes_version = var.kubernetes_version

    node_source_details {
        source_type = "IMAGE"
        image_id    = "ocid1.image.oc1.sa-saopaulo-1.aaaaaaaa5wpgbx3zndwgb4rxmrus4rnwuf3u22k57srp2qdhf3sz76ruve5q"
    }

    node_config_details {
        placement_configs {
            availability_domain = var.availability_domain
            subnet_id           = var.subnet_ids[0]
        }

        size = var.node_pool_size
    }

    node_metadata = {
      "role": "main"
    }

    ssh_public_key = var.ssh_public_key
}
