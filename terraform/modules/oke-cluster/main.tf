resource "oci_containerengine_cluster" "oke-cluster" {
  compartment_id = var.compartment_id
  kubernetes_version = var.kubernetes_version
  name = "postoaki-prod-cluster"
  vcn_id = var.vcn_id

  endpoint_config {
    is_public_ip_enabled = true    
    subnet_id  = var.subnet_id
  }

  options {
    add_ons{
      is_kubernetes_dashboard_enabled = false
      is_tiller_enabled = false
    }
    kubernetes_network_config {
      pods_cidr = var.pods_cidr
      services_cidr = var.services_cidr
    }
    service_lb_subnet_ids = [var.subnet_id]
  }  
}