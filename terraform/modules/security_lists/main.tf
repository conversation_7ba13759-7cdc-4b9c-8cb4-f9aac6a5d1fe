resource "oci_core_security_list" "internet_egress" {
  compartment_id = var.compartment_id
  vcn_id         = var.vcn_id
  display_name   = "internet-egress"

  egress_security_rules {
    destination = "0.0.0.0/0"
    protocol    = "all"
  }
}

resource "oci_core_security_list" "vcn_traffic" {
  compartment_id = var.compartment_id
  vcn_id         = var.vcn_id
  display_name   = "vcn-traffic"

  ingress_security_rules {
    source   = var.vcn_cidr_block
    protocol = "all"
  }

  egress_security_rules {
    destination = var.vcn_cidr_block
    protocol    = "all"
  }
}

resource "oci_core_security_list" "http_ingress" {
  compartment_id = var.compartment_id
  vcn_id         = var.vcn_id
  display_name   = "http-ingress"

  ingress_security_rules {
    source   = "0.0.0.0/0"
    protocol = "6"
    tcp_options {
      min = 80
      max = 80
    }
  }
}

resource "oci_core_security_list" "https_ingress" {
  compartment_id = var.compartment_id
  vcn_id         = var.vcn_id
  display_name   = "http-ingress"

  ingress_security_rules {
    source   = "0.0.0.0/0"
    protocol = "6"
    tcp_options {
      min = 443
      max = 443
    }
  }
}

resource "oci_core_security_list" "k8s_api_ingress" {
  compartment_id = var.compartment_id
  vcn_id         = var.vcn_id
  display_name   = "k8s_api_ingress"

  ingress_security_rules {
    source   = "0.0.0.0/0"
    protocol = "6"
    tcp_options {
      min = 6443
      max = 6443
    }
  }
}