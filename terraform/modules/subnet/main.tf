resource "oci_core_subnet" "subnet" {
    cidr_block          = var.subnet_cidr_block
    compartment_id      = var.compartment_id
    vcn_id              = var.vcn_id
    display_name        = var.subnet_display_name
    dns_label           = var.subnet_dns_label
    route_table_id      = var.route_table_id
    prohibit_public_ip_on_vnic = false
    security_list_ids   = var.security_list_ids
}

resource "oci_core_subnet" "subnet_ad" {
    availability_domain = var.availability_domain
    cidr_block          = "***********/18"
    compartment_id      = var.compartment_id
    vcn_id              = var.vcn_id
    route_table_id      = var.route_table_id
    display_name        = "${var.subnet_display_name}ad"
    dns_label           = "${var.subnet_dns_label}ad"
    prohibit_public_ip_on_vnic = false
    security_list_ids   = var.security_list_ids
}