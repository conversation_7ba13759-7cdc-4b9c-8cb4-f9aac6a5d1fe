variable "tenancy_ocid" {}
variable "user_ocid" {}
variable "fingerprint" {}
variable "private_key_path" {}
variable "region" {}
variable "compartment_id" {}
variable "vcn_cidr_block" {}
variable "vcn_dns_label" {}
variable "vcn_display_name" {}
variable "subnet_cidr_block" {}
variable "subnet_display_name" {}
variable "subnet_dns_label" {}
variable "oke_pods_cidr" {}
variable "oke_services_cidr" { }
variable "node_pool_size" {}
variable "availability_domain" {}
variable "internet_gateway_display_name" {}
variable "route_table_display_name" {}
variable "kubernetes_version" {
  default = "v1.29.1"
}