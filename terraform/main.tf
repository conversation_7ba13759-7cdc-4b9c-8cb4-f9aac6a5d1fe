provider "oci" {
  tenancy_ocid     = var.tenancy_ocid
  user_ocid        = var.user_ocid
  fingerprint      = var.fingerprint
  private_key_path = "/home/<USER>/Dropbox/oci/sessions/DEFAULT/chave_sem_senha.pem"
  region           = var.region
}

module "oke-cluster" {
  source = "./modules/oke-cluster"

  compartment_id = var.compartment_id
  vcn_id = module.vcn.vcn_id
  kubernetes_version = var.kubernetes_version
  subnet_id = module.subnet.subnet_id
  pods_cidr = var.oke_pods_cidr
  services_cidr = var.oke_services_cidr
}

module "vcn" {
  source = "./modules/vcn"

  cidr_block     = var.vcn_cidr_block
  compartment_id = var.compartment_id
  display_name   = var.vcn_display_name
  dns_label      = var.vcn_dns_label
}

module "subnet" {
  source = "./modules/subnet"

  availability_domain = var.availability_domain
  subnet_cidr_block   = var.subnet_cidr_block
  compartment_id      = var.compartment_id
  vcn_id              = module.vcn.vcn_id
  subnet_display_name = var.subnet_display_name
  subnet_dns_label    = "postoaki"
  route_table_id      = module.route_table.route_table_id
  security_list_ids    = [
    module.security_lists.internet_egress_security_list_id, 
    module.security_lists.vcn_traffic_security_list_id, 
    module.security_lists.http_ingress_security_list_id, 
    module.security_lists.https_ingress_security_list_id,
    module.security_lists.k8s_api_ingress_id]
}

module "security_lists" {
  source = "./modules/security_lists"

  compartment_id = var.compartment_id
  vcn_id         = module.vcn.vcn_id
  vcn_cidr_block = var.vcn_cidr_block
}


module "node_pool" {
  source = "./modules/node_pool"

  compartment_id = var.compartment_id
  kubernetes_version = var.kubernetes_version
  availability_domain = var.availability_domain
  cluster_id       = module.oke-cluster.cluster_id
  node_pool_size   = var.node_pool_size
  subnet_prod_id  = "ocid1.subnet.oc1.sa-saopaulo-1.aaaaaaaatfqkl76th2vdmmf5b6vpamjw2p6c7lqx4h3hq2he6x7agikml2ia"
  subnet_ids       = [module.subnet.subnet_ad_id]
  ssh_public_key   = file("~/.ssh/id_rsa.pub")
}

module "internet_gateway" {
  source        = "./modules/internet_gateway"
  compartment_id = var.compartment_id
  display_name   = var.internet_gateway_display_name
  vcn_id         = module.vcn.vcn_id
}

module "route_table" {
  source              = "./modules/route_table"
  compartment_id      = var.compartment_id
  display_name        = var.route_table_display_name
  vcn_id              = module.vcn.vcn_id
  destination         = "0.0.0.0/0"
  internet_gateway_id = module.internet_gateway.internet_gateway_id
}
