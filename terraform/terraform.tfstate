{"version": 4, "terraform_version": "1.9.2", "serial": 45, "lineage": "5d11f224-7e49-0156-50e5-5423ba3b5801", "outputs": {}, "resources": [{"module": "module.internet_gateway", "mode": "managed", "type": "oci_core_internet_gateway", "name": "igw", "provider": "provider[\"registry.terraform.io/hashicorp/oci\"]", "instances": [{"schema_version": 0, "attributes": {"compartment_id": "ocid1.compartment.oc1..aaaaaaaaovof4p4uveta2pmm3wb25ubk3tfwqrtarimaq2apoabgbprnmmoa", "defined_tags": {"Oracle-Tags.CreatedBy": "oracleidentitycloudservice/<EMAIL>", "Oracle-Tags.CreatedOn": "2024-07-24T19:53:16.643Z"}, "display_name": "postoaki_igw", "enabled": true, "freeform_tags": {}, "id": "ocid1.internetgateway.oc1.sa-saopaulo-1.aaaaaaaaph4uqm6ipjezkaeqomlflppagki2jw46sdb3vfhwtca6umgps3wq", "route_table_id": null, "state": "AVAILABLE", "time_created": "2024-07-24 19:53:16.678 +0000 UTC", "timeouts": null, "vcn_id": "ocid1.vcn.oc1.sa-saopaulo-1.amaaaaaaiwfci7qapgzwg76wxoiuyr4wdicmmrsv257se7isn3hvsdy7pj2q"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19", "dependencies": ["module.vcn.oci_core_vcn.vcn"]}]}, {"module": "module.node_pool", "mode": "managed", "type": "oci_containerengine_node_pool", "name": "node_pool", "provider": "provider[\"registry.terraform.io/hashicorp/oci\"]", "instances": [{"schema_version": 0, "attributes": {"cluster_id": "ocid1.cluster.oc1.sa-saopaulo-1.aaaaaaaaagcar2aozh7ndsvdbxxix3h6ogy46e4xf3wu4pgs6cxz2vhqlquq", "compartment_id": "ocid1.compartment.oc1..aaaaaaaaovof4p4uveta2pmm3wb25ubk3tfwqrtarimaq2apoabgbprnmmoa", "defined_tags": {"Oracle-Tags.CreatedBy": "oracleidentitycloudservice/<EMAIL>", "Oracle-Tags.CreatedOn": "2024-07-24T20:10:29.534Z"}, "freeform_tags": {}, "id": "ocid1.nodepool.oc1.sa-saopaulo-1.aaaaaaaaunnqihwqz4wkpeok3j4vcwwlfuhurdm5r6b3imdxin6gdirmvk2a", "initial_node_labels": [], "kubernetes_version": "v1.29.1", "lifecycle_details": null, "name": "node-pool-1", "node_config_details": [{"defined_tags": {}, "freeform_tags": {}, "is_pv_encryption_in_transit_enabled": false, "kms_key_id": "", "node_pool_pod_network_option_details": [{"cni_type": "FLANNEL_OVERLAY", "max_pods_per_node": 0, "pod_nsg_ids": [], "pod_subnet_ids": []}], "nsg_ids": [], "placement_configs": [{"availability_domain": "zdYa:SA-SAOPAULO-1-AD-1", "capacity_reservation_id": "", "fault_domains": [], "preemptible_node_config": [], "subnet_id": "ocid1.subnet.oc1.sa-saopaulo-1.aaaaaaaami7ocgm2dt5afka7jam6p7ztdvbpprjq4q5hsmypry6v44oov7ua"}], "size": 3}], "node_eviction_node_pool_settings": [], "node_image_id": "ocid1.image.oc1.sa-saopaulo-1.aaaaaaaa5wpgbx3zndwgb4rxmrus4rnwuf3u22k57srp2qdhf3sz76ruve5q", "node_image_name": "Oracle-Linux-8.9-2024.04.19-0", "node_metadata": {"role": "main"}, "node_pool_cycling_details": [], "node_shape": "VM.Standard.E5.Flex", "node_shape_config": [{"memory_in_gbs": 4, "ocpus": 2}], "node_source": [{"image_id": "ocid1.image.oc1.sa-saopaulo-1.aaaaaaaa5wpgbx3zndwgb4rxmrus4rnwuf3u22k57srp2qdhf3sz76ruve5q", "source_name": "Oracle-Linux-8.9-2024.04.19-0", "source_type": "IMAGE"}], "node_source_details": [{"boot_volume_size_in_gbs": "", "image_id": "ocid1.image.oc1.sa-saopaulo-1.aaaaaaaa5wpgbx3zndwgb4rxmrus4rnwuf3u22k57srp2qdhf3sz76ruve5q", "source_type": "IMAGE"}], "nodes": [{"availability_domain": "zdYa:SA-SAOPAULO-1-AD-1", "defined_tags": {}, "error": [], "fault_domain": "FAULT-DOMAIN-1", "freeform_tags": {}, "id": "ocid1.instance.oc1.sa-saopaulo-1.antxeljriwfci7qccq3pyembnvvqi7ryje3ktn64u3kgdkv46iuahe5demha", "kubernetes_version": "v1.29.1", "lifecycle_details": "", "name": "oke-cxz2vhqlquq-n6gdirmvk2a-s6v44oov7ua-0", "node_pool_id": "ocid1.nodepool.oc1.sa-saopaulo-1.aaaaaaaaunnqihwqz4wkpeok3j4vcwwlfuhurdm5r6b3imdxin6gdirmvk2a", "private_ip": "*************", "public_ip": "**************", "state": "ACTIVE", "subnet_id": "ocid1.subnet.oc1.sa-saopaulo-1.aaaaaaaami7ocgm2dt5afka7jam6p7ztdvbpprjq4q5hsmypry6v44oov7ua"}, {"availability_domain": "zdYa:SA-SAOPAULO-1-AD-1", "defined_tags": {}, "error": [], "fault_domain": "FAULT-DOMAIN-2", "freeform_tags": {}, "id": "ocid1.instance.oc1.sa-saopaulo-1.antxeljriwfci7qchp2fqo57uxjxnrqyb7q62nsco3nogtupj4oni6nohzka", "kubernetes_version": "v1.29.1", "lifecycle_details": "", "name": "oke-cxz2vhqlquq-n6gdirmvk2a-s6v44oov7ua-1", "node_pool_id": "ocid1.nodepool.oc1.sa-saopaulo-1.aaaaaaaaunnqihwqz4wkpeok3j4vcwwlfuhurdm5r6b3imdxin6gdirmvk2a", "private_ip": "**************", "public_ip": "**************", "state": "ACTIVE", "subnet_id": "ocid1.subnet.oc1.sa-saopaulo-1.aaaaaaaami7ocgm2dt5afka7jam6p7ztdvbpprjq4q5hsmypry6v44oov7ua"}, {"availability_domain": "zdYa:SA-SAOPAULO-1-AD-1", "defined_tags": {}, "error": [], "fault_domain": "FAULT-DOMAIN-3", "freeform_tags": {}, "id": "ocid1.instance.oc1.sa-saopaulo-1.antxeljriwfci7qcfzoxbfwoonqrl4xo4ps5jshf3pyyyipz5ga2fjmbavsq", "kubernetes_version": "v1.29.1", "lifecycle_details": "", "name": "oke-cxz2vhqlquq-n6gdirmvk2a-s6v44oov7ua-2", "node_pool_id": "ocid1.nodepool.oc1.sa-saopaulo-1.aaaaaaaaunnqihwqz4wkpeok3j4vcwwlfuhurdm5r6b3imdxin6gdirmvk2a", "private_ip": "*************", "public_ip": "************", "state": "ACTIVE", "subnet_id": "ocid1.subnet.oc1.sa-saopaulo-1.aaaaaaaami7ocgm2dt5afka7jam6p7ztdvbpprjq4q5hsmypry6v44oov7ua"}], "quantity_per_subnet": 0, "ssh_public_key": "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQDG0U7+ofHS17yk9k9eE0LvPElIQEHpuCoVOHtzy+kRv2LAblw+KyxIBUvjspOUX2gIx4+i3azUXhnI5P90lJWPacsRpXgQKiu1KejloWSnlY+gNkPrs2V+eglMQ2KW13H1v659Kq0C3/IhRtNFwDtM3yWhcFS+XeD5V2sRzB/8KifXz7Fbdl61Ky8tKwguwlq2WTaewLAeL6lO/8s4h+j/UKOIS7TvvQx7fJzydv2CZlUH0+PuFQbv6iDRy6dSSlkYXDX6Rv289M+5bM+eT02XnAdJO2ybXZz0oZk0fcAzid+LxkQ2/1/WqwE33R+NiZt+VDSfIXSgJKsG1r0Rkv2H <EMAIL>\n", "state": "ACTIVE", "subnet_ids": ["ocid1.subnet.oc1.sa-saopaulo-1.aaaaaaaami7ocgm2dt5afka7jam6p7ztdvbpprjq4q5hsmypry6v44oov7ua"], "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAwLCJkZWxldGUiOjMwMDAwMDAwMDAwMDAsInVwZGF0ZSI6MzAwMDAwMDAwMDAwMH19", "dependencies": ["module.internet_gateway.oci_core_internet_gateway.igw", "module.oke-cluster.oci_containerengine_cluster.oke-cluster", "module.route_table.oci_core_route_table.rt", "module.security_lists.oci_core_security_list.http_ingress", "module.security_lists.oci_core_security_list.https_ingress", "module.security_lists.oci_core_security_list.internet_egress", "module.security_lists.oci_core_security_list.k8s_api_ingress", "module.security_lists.oci_core_security_list.vcn_traffic", "module.subnet.oci_core_subnet.subnet", "module.subnet.oci_core_subnet.subnet_ad", "module.vcn.oci_core_vcn.vcn"]}]}, {"module": "module.oke-cluster", "mode": "managed", "type": "oci_containerengine_cluster", "name": "oke-cluster", "provider": "provider[\"registry.terraform.io/hashicorp/oci\"]", "instances": [{"schema_version": 0, "attributes": {"available_kubernetes_upgrades": ["v1.30.1"], "cluster_pod_network_options": [{"cni_type": "FLANNEL_OVERLAY"}], "compartment_id": "ocid1.compartment.oc1..aaaaaaaaovof4p4uveta2pmm3wb25ubk3tfwqrtarimaq2apoabgbprnmmoa", "defined_tags": {"Oracle-Tags.CreatedBy": "oracleidentitycloudservice/<EMAIL>", "Oracle-Tags.CreatedOn": "2024-07-24T19:57:19.004Z"}, "endpoint_config": [{"is_public_ip_enabled": true, "nsg_ids": [], "subnet_id": "ocid1.subnet.oc1.sa-saopaulo-1.aaaaaaaax7apkkz4j662wfez2icfxnj5m5y7tjygkplgz23e3jww2scl4tfq"}], "endpoints": [{"kubernetes": "", "private_endpoint": "***************:6443", "public_endpoint": "**************:6443", "vcn_hostname_endpoint": "cxz2vhqlquq.postoaki.postoaki.oraclevcn.com:6443"}], "freeform_tags": {}, "id": "ocid1.cluster.oc1.sa-saopaulo-1.aaaaaaaaagcar2aozh7ndsvdbxxix3h6ogy46e4xf3wu4pgs6cxz2vhqlquq", "image_policy_config": [{"is_policy_enabled": false, "key_details": []}], "kms_key_id": null, "kubernetes_version": "v1.29.1", "lifecycle_details": null, "metadata": [{"created_by_user_id": "ocid1.user.oc1..aaaaaaaaqcqglikmptvoxdnhzrztqhsiy2jsk5plnqjq4gqk54lk6s4julna", "created_by_work_request_id": "ocid1.clustersworkrequest.oc1.sa-saopaulo-1.aaaaaaaam4rpo7occq4axv2omhxnvfozavvdtsigbcyiwvg3swsnxkgv4smq", "deleted_by_user_id": "", "deleted_by_work_request_id": "", "time_created": "2024-07-24 19:57:19 +0000 UTC", "time_credential_expiration": "2029-07-24 19:57:58 +0000 UTC", "time_deleted": "", "time_updated": "", "updated_by_user_id": "", "updated_by_work_request_id": ""}], "name": "postoaki-prod-cluster", "options": [{"add_ons": [{"is_kubernetes_dashboard_enabled": false, "is_tiller_enabled": false}], "admission_controller_options": [{"is_pod_security_policy_enabled": false}], "kubernetes_network_config": [{"pods_cidr": "10.244.0.0/16", "services_cidr": "10.96.0.0/16"}], "persistent_volume_config": [{"defined_tags": {}, "freeform_tags": {}}], "service_lb_config": [{"defined_tags": {}, "freeform_tags": {}}], "service_lb_subnet_ids": ["ocid1.subnet.oc1.sa-saopaulo-1.aaaaaaaax7apkkz4j662wfez2icfxnj5m5y7tjygkplgz23e3jww2scl4tfq"]}], "state": "ACTIVE", "timeouts": null, "type": "BASIC_CLUSTER", "vcn_id": "ocid1.vcn.oc1.sa-saopaulo-1.amaaaaaaiwfci7qapgzwg76wxoiuyr4wdicmmrsv257se7isn3hvsdy7pj2q"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozNjAwMDAwMDAwMDAwLCJkZWxldGUiOjM2MDAwMDAwMDAwMDAsInVwZGF0ZSI6MzYwMDAwMDAwMDAwMH19", "dependencies": ["module.internet_gateway.oci_core_internet_gateway.igw", "module.route_table.oci_core_route_table.rt", "module.security_lists.oci_core_security_list.http_ingress", "module.security_lists.oci_core_security_list.https_ingress", "module.security_lists.oci_core_security_list.internet_egress", "module.security_lists.oci_core_security_list.k8s_api_ingress", "module.security_lists.oci_core_security_list.vcn_traffic", "module.subnet.oci_core_subnet.subnet", "module.vcn.oci_core_vcn.vcn"]}]}, {"module": "module.route_table", "mode": "managed", "type": "oci_core_route_table", "name": "rt", "provider": "provider[\"registry.terraform.io/hashicorp/oci\"]", "instances": [{"schema_version": 0, "attributes": {"compartment_id": "ocid1.compartment.oc1..aaaaaaaaovof4p4uveta2pmm3wb25ubk3tfwqrtarimaq2apoabgbprnmmoa", "defined_tags": {"Oracle-Tags.CreatedBy": "oracleidentitycloudservice/<EMAIL>", "Oracle-Tags.CreatedOn": "2024-07-24T19:53:17.287Z"}, "display_name": "postoaki_rt", "freeform_tags": {}, "id": "ocid1.routetable.oc1.sa-saopaulo-1.aaaaaaaaxwt3l3pjbjdkzwggziwjmqhep7inoirslvgcb5xr5pczeduzjetq", "route_rules": [{"cidr_block": "", "description": "", "destination": "0.0.0.0/0", "destination_type": "CIDR_BLOCK", "network_entity_id": "ocid1.internetgateway.oc1.sa-saopaulo-1.aaaaaaaaph4uqm6ipjezkaeqomlflppagki2jw46sdb3vfhwtca6umgps3wq", "route_type": ""}], "state": "AVAILABLE", "time_created": "2024-07-24 19:53:17.309 +0000 UTC", "timeouts": null, "vcn_id": "ocid1.vcn.oc1.sa-saopaulo-1.amaaaaaaiwfci7qapgzwg76wxoiuyr4wdicmmrsv257se7isn3hvsdy7pj2q"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19", "dependencies": ["module.internet_gateway.oci_core_internet_gateway.igw", "module.vcn.oci_core_vcn.vcn"]}]}, {"module": "module.security_lists", "mode": "managed", "type": "oci_core_security_list", "name": "http_ingress", "provider": "provider[\"registry.terraform.io/hashicorp/oci\"]", "instances": [{"schema_version": 0, "attributes": {"compartment_id": "ocid1.compartment.oc1..aaaaaaaaovof4p4uveta2pmm3wb25ubk3tfwqrtarimaq2apoabgbprnmmoa", "defined_tags": {"Oracle-Tags.CreatedBy": "oracleidentitycloudservice/<EMAIL>", "Oracle-Tags.CreatedOn": "2024-07-24T19:53:16.846Z"}, "display_name": "http-ingress", "egress_security_rules": [], "freeform_tags": {}, "id": "ocid1.securitylist.oc1.sa-saopaulo-1.aaaaaaaakwqjeyjxtrzkvixn7swy5zuk6sedvfgcaqhbcssjqrqd437nfkyq", "ingress_security_rules": [{"description": "", "icmp_options": [], "protocol": "6", "source": "0.0.0.0/0", "source_type": "CIDR_BLOCK", "stateless": false, "tcp_options": [{"max": 80, "min": 80, "source_port_range": []}], "udp_options": []}], "state": "AVAILABLE", "time_created": "2024-07-24 19:53:16.882 +0000 UTC", "timeouts": null, "vcn_id": "ocid1.vcn.oc1.sa-saopaulo-1.amaaaaaaiwfci7qapgzwg76wxoiuyr4wdicmmrsv257se7isn3hvsdy7pj2q"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19", "dependencies": ["module.vcn.oci_core_vcn.vcn"]}]}, {"module": "module.security_lists", "mode": "managed", "type": "oci_core_security_list", "name": "https_ingress", "provider": "provider[\"registry.terraform.io/hashicorp/oci\"]", "instances": [{"schema_version": 0, "attributes": {"compartment_id": "ocid1.compartment.oc1..aaaaaaaaovof4p4uveta2pmm3wb25ubk3tfwqrtarimaq2apoabgbprnmmoa", "defined_tags": {"Oracle-Tags.CreatedBy": "oracleidentitycloudservice/<EMAIL>", "Oracle-Tags.CreatedOn": "2024-07-24T19:53:16.773Z"}, "display_name": "http-ingress", "egress_security_rules": [], "freeform_tags": {}, "id": "ocid1.securitylist.oc1.sa-saopaulo-1.aaaaaaaaya5ljgefsjxtmtteqdgrob5u53y7amq35ah3y2ijfrfpefyntemq", "ingress_security_rules": [{"description": "", "icmp_options": [], "protocol": "6", "source": "0.0.0.0/0", "source_type": "CIDR_BLOCK", "stateless": false, "tcp_options": [{"max": 443, "min": 443, "source_port_range": []}], "udp_options": []}], "state": "AVAILABLE", "time_created": "2024-07-24 19:53:16.825 +0000 UTC", "timeouts": null, "vcn_id": "ocid1.vcn.oc1.sa-saopaulo-1.amaaaaaaiwfci7qapgzwg76wxoiuyr4wdicmmrsv257se7isn3hvsdy7pj2q"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19", "dependencies": ["module.vcn.oci_core_vcn.vcn"]}]}, {"module": "module.security_lists", "mode": "managed", "type": "oci_core_security_list", "name": "internet_egress", "provider": "provider[\"registry.terraform.io/hashicorp/oci\"]", "instances": [{"schema_version": 0, "attributes": {"compartment_id": "ocid1.compartment.oc1..aaaaaaaaovof4p4uveta2pmm3wb25ubk3tfwqrtarimaq2apoabgbprnmmoa", "defined_tags": {"Oracle-Tags.CreatedBy": "oracleidentitycloudservice/<EMAIL>", "Oracle-Tags.CreatedOn": "2024-07-24T19:53:16.773Z"}, "display_name": "internet-egress", "egress_security_rules": [{"description": "", "destination": "0.0.0.0/0", "destination_type": "CIDR_BLOCK", "icmp_options": [], "protocol": "all", "stateless": false, "tcp_options": [], "udp_options": []}], "freeform_tags": {}, "id": "ocid1.securitylist.oc1.sa-saopaulo-1.aaaaaaaaktotqxkpqvvmsyve7izqi7xp3yykkkpwv2qklmo6tj4bdlnjcnja", "ingress_security_rules": [], "state": "AVAILABLE", "time_created": "2024-07-24 19:53:16.801 +0000 UTC", "timeouts": null, "vcn_id": "ocid1.vcn.oc1.sa-saopaulo-1.amaaaaaaiwfci7qapgzwg76wxoiuyr4wdicmmrsv257se7isn3hvsdy7pj2q"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19", "dependencies": ["module.vcn.oci_core_vcn.vcn"]}]}, {"module": "module.security_lists", "mode": "managed", "type": "oci_core_security_list", "name": "k8s_api_ingress", "provider": "provider[\"registry.terraform.io/hashicorp/oci\"]", "instances": [{"schema_version": 0, "attributes": {"compartment_id": "ocid1.compartment.oc1..aaaaaaaaovof4p4uveta2pmm3wb25ubk3tfwqrtarimaq2apoabgbprnmmoa", "defined_tags": {"Oracle-Tags.CreatedBy": "oracleidentitycloudservice/<EMAIL>", "Oracle-Tags.CreatedOn": "2024-07-24T19:53:16.789Z"}, "display_name": "k8s_api_ingress", "egress_security_rules": [], "freeform_tags": {}, "id": "ocid1.securitylist.oc1.sa-saopaulo-1.aaaaaaaa7k7gkdz25rlwthtkp3efvly6dp32ttlsqei43mtdfcbyxhjklmqq", "ingress_security_rules": [{"description": "", "icmp_options": [], "protocol": "6", "source": "0.0.0.0/0", "source_type": "CIDR_BLOCK", "stateless": false, "tcp_options": [{"max": 6443, "min": 6443, "source_port_range": []}], "udp_options": []}], "state": "AVAILABLE", "time_created": "2024-07-24 19:53:16.816 +0000 UTC", "timeouts": null, "vcn_id": "ocid1.vcn.oc1.sa-saopaulo-1.amaaaaaaiwfci7qapgzwg76wxoiuyr4wdicmmrsv257se7isn3hvsdy7pj2q"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19", "dependencies": ["module.vcn.oci_core_vcn.vcn"]}]}, {"module": "module.security_lists", "mode": "managed", "type": "oci_core_security_list", "name": "vcn_traffic", "provider": "provider[\"registry.terraform.io/hashicorp/oci\"]", "instances": [{"schema_version": 0, "attributes": {"compartment_id": "ocid1.compartment.oc1..aaaaaaaaovof4p4uveta2pmm3wb25ubk3tfwqrtarimaq2apoabgbprnmmoa", "defined_tags": {"Oracle-Tags.CreatedBy": "oracleidentitycloudservice/<EMAIL>", "Oracle-Tags.CreatedOn": "2024-07-24T19:53:16.638Z"}, "display_name": "vcn-traffic", "egress_security_rules": [{"description": "", "destination": "***********/16", "destination_type": "CIDR_BLOCK", "icmp_options": [], "protocol": "all", "stateless": false, "tcp_options": [], "udp_options": []}], "freeform_tags": {}, "id": "ocid1.securitylist.oc1.sa-saopaulo-1.aaaaaaaao7ypbvjpf75hd5hisv7jdwo4ban4xn6ycag5q4fxtexgn267st6a", "ingress_security_rules": [{"description": "", "icmp_options": [], "protocol": "all", "source": "***********/16", "source_type": "CIDR_BLOCK", "stateless": false, "tcp_options": [], "udp_options": []}], "state": "AVAILABLE", "time_created": "2024-07-24 19:53:16.667 +0000 UTC", "timeouts": null, "vcn_id": "ocid1.vcn.oc1.sa-saopaulo-1.amaaaaaaiwfci7qapgzwg76wxoiuyr4wdicmmrsv257se7isn3hvsdy7pj2q"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19", "dependencies": ["module.vcn.oci_core_vcn.vcn"]}]}, {"module": "module.subnet", "mode": "managed", "type": "oci_core_subnet", "name": "subnet", "provider": "provider[\"registry.terraform.io/hashicorp/oci\"]", "instances": [{"schema_version": 0, "attributes": {"availability_domain": null, "cidr_block": "************/18", "compartment_id": "ocid1.compartment.oc1..aaaaaaaaovof4p4uveta2pmm3wb25ubk3tfwqrtarimaq2apoabgbprnmmoa", "defined_tags": {"Oracle-Tags.CreatedBy": "oracleidentitycloudservice/<EMAIL>", "Oracle-Tags.CreatedOn": "2024-07-24T19:54:12.938Z"}, "dhcp_options_id": "ocid1.dhcpoptions.oc1.sa-saopaulo-1.aaaaaaaaj3bn2xmy2t2llhxg6f2lmf6znmhv5g4owqavd44x3krplr7byr7a", "display_name": "postoaki_subnet", "dns_label": "<PERSON><PERSON><PERSON>", "freeform_tags": {}, "id": "ocid1.subnet.oc1.sa-saopaulo-1.aaaaaaaax7apkkz4j662wfez2icfxnj5m5y7tjygkplgz23e3jww2scl4tfq", "ipv6cidr_block": null, "ipv6cidr_blocks": [], "ipv6virtual_router_ip": null, "prohibit_internet_ingress": false, "prohibit_public_ip_on_vnic": false, "route_table_id": "ocid1.routetable.oc1.sa-saopaulo-1.aaaaaaaaxwt3l3pjbjdkzwggziwjmqhep7inoirslvgcb5xr5pczeduzjetq", "security_list_ids": ["ocid1.securitylist.oc1.sa-saopaulo-1.aaaaaaaa7k7gkdz25rlwthtkp3efvly6dp32ttlsqei43mtdfcbyxhjklmqq", "ocid1.securitylist.oc1.sa-saopaulo-1.aaaaaaaaktotqxkpqvvmsyve7izqi7xp3yykkkpwv2qklmo6tj4bdlnjcnja", "ocid1.securitylist.oc1.sa-saopaulo-1.aaaaaaaakwqjeyjxtrzkvixn7swy5zuk6sedvfgcaqhbcssjqrqd437nfkyq", "ocid1.securitylist.oc1.sa-saopaulo-1.aaaaaaaao7ypbvjpf75hd5hisv7jdwo4ban4xn6ycag5q4fxtexgn267st6a", "ocid1.securitylist.oc1.sa-saopaulo-1.aaaaaaaaya5ljgefsjxtmtteqdgrob5u53y7amq35ah3y2ijfrfpefyntemq"], "state": "AVAILABLE", "subnet_domain_name": "postoaki.postoaki.oraclevcn.com", "time_created": "2024-07-24 19:54:13.904 +0000 UTC", "timeouts": null, "vcn_id": "ocid1.vcn.oc1.sa-saopaulo-1.amaaaaaaiwfci7qapgzwg76wxoiuyr4wdicmmrsv257se7isn3hvsdy7pj2q", "virtual_router_ip": "************", "virtual_router_mac": "00:00:17:FA:22:20"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19", "dependencies": ["module.internet_gateway.oci_core_internet_gateway.igw", "module.route_table.oci_core_route_table.rt", "module.security_lists.oci_core_security_list.http_ingress", "module.security_lists.oci_core_security_list.https_ingress", "module.security_lists.oci_core_security_list.internet_egress", "module.security_lists.oci_core_security_list.k8s_api_ingress", "module.security_lists.oci_core_security_list.vcn_traffic", "module.vcn.oci_core_vcn.vcn"]}]}, {"module": "module.subnet", "mode": "managed", "type": "oci_core_subnet", "name": "subnet_ad", "provider": "provider[\"registry.terraform.io/hashicorp/oci\"]", "instances": [{"schema_version": 0, "attributes": {"availability_domain": "zdYa:SA-SAOPAULO-1-AD-1", "cidr_block": "***********/18", "compartment_id": "ocid1.compartment.oc1..aaaaaaaaovof4p4uveta2pmm3wb25ubk3tfwqrtarimaq2apoabgbprnmmoa", "defined_tags": {"Oracle-Tags.CreatedBy": "oracleidentitycloudservice/<EMAIL>", "Oracle-Tags.CreatedOn": "2024-07-24T19:56:40.705Z"}, "dhcp_options_id": "ocid1.dhcpoptions.oc1.sa-saopaulo-1.aaaaaaaaj3bn2xmy2t2llhxg6f2lmf6znmhv5g4owqavd44x3krplr7byr7a", "display_name": "postoaki_subnetad", "dns_label": "postoakiad", "freeform_tags": {}, "id": "ocid1.subnet.oc1.sa-saopaulo-1.aaaaaaaami7ocgm2dt5afka7jam6p7ztdvbpprjq4q5hsmypry6v44oov7ua", "ipv6cidr_block": null, "ipv6cidr_blocks": [], "ipv6virtual_router_ip": null, "prohibit_internet_ingress": false, "prohibit_public_ip_on_vnic": false, "route_table_id": "ocid1.routetable.oc1.sa-saopaulo-1.aaaaaaaaxwt3l3pjbjdkzwggziwjmqhep7inoirslvgcb5xr5pczeduzjetq", "security_list_ids": ["ocid1.securitylist.oc1.sa-saopaulo-1.aaaaaaaa7k7gkdz25rlwthtkp3efvly6dp32ttlsqei43mtdfcbyxhjklmqq", "ocid1.securitylist.oc1.sa-saopaulo-1.aaaaaaaaktotqxkpqvvmsyve7izqi7xp3yykkkpwv2qklmo6tj4bdlnjcnja", "ocid1.securitylist.oc1.sa-saopaulo-1.aaaaaaaakwqjeyjxtrzkvixn7swy5zuk6sedvfgcaqhbcssjqrqd437nfkyq", "ocid1.securitylist.oc1.sa-saopaulo-1.aaaaaaaao7ypbvjpf75hd5hisv7jdwo4ban4xn6ycag5q4fxtexgn267st6a", "ocid1.securitylist.oc1.sa-saopaulo-1.aaaaaaaaya5ljgefsjxtmtteqdgrob5u53y7amq35ah3y2ijfrfpefyntemq"], "state": "AVAILABLE", "subnet_domain_name": "postoakiad.postoaki.oraclevcn.com", "time_created": "2024-07-24 19:56:40.782 +0000 UTC", "timeouts": null, "vcn_id": "ocid1.vcn.oc1.sa-saopaulo-1.amaaaaaaiwfci7qapgzwg76wxoiuyr4wdicmmrsv257se7isn3hvsdy7pj2q", "virtual_router_ip": "***********", "virtual_router_mac": "00:00:17:FA:22:20"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19", "dependencies": ["module.internet_gateway.oci_core_internet_gateway.igw", "module.route_table.oci_core_route_table.rt", "module.security_lists.oci_core_security_list.http_ingress", "module.security_lists.oci_core_security_list.https_ingress", "module.security_lists.oci_core_security_list.internet_egress", "module.security_lists.oci_core_security_list.k8s_api_ingress", "module.security_lists.oci_core_security_list.vcn_traffic", "module.vcn.oci_core_vcn.vcn"]}]}, {"module": "module.vcn", "mode": "managed", "type": "oci_core_vcn", "name": "vcn", "provider": "provider[\"registry.terraform.io/hashicorp/oci\"]", "instances": [{"schema_version": 0, "attributes": {"byoipv6cidr_blocks": [], "byoipv6cidr_details": [], "cidr_block": "***********/16", "cidr_blocks": ["***********/16"], "compartment_id": "ocid1.compartment.oc1..aaaaaaaaovof4p4uveta2pmm3wb25ubk3tfwqrtarimaq2apoabgbprnmmoa", "default_dhcp_options_id": "ocid1.dhcpoptions.oc1.sa-saopaulo-1.aaaaaaaaj3bn2xmy2t2llhxg6f2lmf6znmhv5g4owqavd44x3krplr7byr7a", "default_route_table_id": "ocid1.routetable.oc1.sa-saopaulo-1.aaaaaaaaaoytanmdzjyokmg6v6nxzovaqq3obg4ncvrvtamyd2ubnrwy3qqq", "default_security_list_id": "ocid1.securitylist.oc1.sa-saopaulo-1.aaaaaaaaqwdooqz2o646mpnfnjinteqancgpxvv4wjaxrmvyjjnowqypggyq", "defined_tags": {"Oracle-Tags.CreatedBy": "oracleidentitycloudservice/<EMAIL>", "Oracle-Tags.CreatedOn": "2024-07-24T19:53:15.960Z"}, "display_name": "postoaki_vcn", "dns_label": "<PERSON><PERSON><PERSON>", "freeform_tags": {}, "id": "ocid1.vcn.oc1.sa-saopaulo-1.amaaaaaaiwfci7qapgzwg76wxoiuyr4wdicmmrsv257se7isn3hvsdy7pj2q", "ipv6cidr_blocks": [], "ipv6private_cidr_blocks": [], "is_ipv6enabled": false, "is_oracle_gua_allocation_enabled": null, "state": "AVAILABLE", "time_created": "2024-07-24 19:53:16.065 +0000 UTC", "timeouts": null, "vcn_domain_name": "postoaki.oraclevcn.com"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19"}]}], "check_results": null}