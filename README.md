# Infraestrutura e DevOps PostoAki

Este projeto contém a infraestrutura e as configurações de DevOps para o projeto PostoAki.

## Pré-requisitos

- Terraform v0.14+
- Oracle Cloud Infrastructure (OCI) CLI

# Configuração

1. In<PERSON>e as ferramentas necessárias mencionadas nos pré-requisitos.
2. Configure o OCI CLI com suas credenciais da Oracle Cloud.
3. Clone este repositório para sua máquina local.

# Uso

1. Navegue até o diretório do projeto.
2. Inicialize o Terraform com `terraform init`.
3. Verifique o plano do Terraform com `terraform plan`.
4. Aplique o plano do Terraform com `terraform apply`.

# Arquitetura

Este projeto utiliza a Oracle Cloud Infrastructure (OCI) para hospedar a infraestrutura do projeto PostoAki.

A arquitetura é composta por um cluster Kubernetes (OKE) que hospeda os serviços do projeto. O cluster é composto por vários node pools, cada um contendo um conjunto de nós que executam os pods do Kubernetes.

Os nós são criados em sub-redes regionais dentro de um Virtual Cloud Network (VCN) na OCI. Cada sub-rede está associada a um Availability Domain específico para garantir a alta disponibilidade dos serviços.

O Terraform é usado para provisionar e gerenciar toda a infraestrutura na OCI. Ele cria o VCN, as sub-redes, o cluster OKE e os node pools. Além disso, ele gerencia as configurações de segurança, como as listas de segurança e as regras de roteamento.

O projeto segue as melhores práticas de DevOps e Infraestrutura como Código (IaC). Todas as alterações na infraestrutura são feitas através do código do Terraform, garantindo que a infraestrutura seja facilmente replicável e rastreável.

# Configuração de Variáveis

As variáveis que precisam ser definidas para este projeto estão localizadas no arquivo `variables.tf`. Este arquivo contém a definição de todas as variáveis usadas nos arquivos de configuração do Terraform.

- `tenancy_ocid`: O OCID (Oracle Cloud Identifier) do seu tenancy na Oracle Cloud Infrastructure. Um tenancy é a conta raiz na Oracle Cloud Infrastructure.

Você pode encontrar o tenancy_ocid (Oracle Cloud Identifier) do seu tenancy na Oracle Cloud Infrastructure (OCI) seguindo estas etapas:

Faça login na Console da Oracle Cloud Infrastructure.
No menu superior direito, clique no seu nome de usuário e selecione "Tenancy Details" no menu suspenso.
Na página "Tenancy Details", você encontrará o OCID do seu tenancy na seção "Tenancy Information".

- `user_ocid`: O OCID do usuário que está executando as operações do Terraform. Este usuário precisa ter as permissões necessárias para criar, modificar e destruir recursos na Oracle Cloud Infrastructure.

Aqui estão os passos para encontrar o user_ocid:

Faça login na Console da Oracle Cloud Infrastructure.
No menu superior direito, clique no seu nome de usuário e selecione "User Settings" no menu suspenso.
Na página "User Settings", você encontrará o OCID do usuário na seção "User Information".

- `fingerprint`: A impressão digital da chave pública SSH do usuário. Esta chave é usada para autenticar o usuário quando ele está fazendo chamadas de API para a Oracle Cloud Infrastructure.

Aqui estão os passos para encontrar o `fingerprint`:

1. Faça login na Console da Oracle Cloud Infrastructure.
2. No menu superior direito, clique no seu nome de usuário e selecione "User Settings" no menu suspenso.
3. Na página "User Settings", clique em "API Keys" no menu à esquerda.
4. Na página "API Keys", você encontrará o `fingerprint` listado para cada chave API.

Por favor, note que você precisa ter as permissões necessárias para visualizar as chaves API. Se você não tiver essas permissões, entre em contato com o administrador da sua conta OCI.

- `private_key_path`: O caminho para a chave privada SSH do usuário no sistema de arquivos local. Esta chave é usada em conjunto com a chave pública para autenticar o usuário.

- `region`: A região da Oracle Cloud Infrastructure onde os recursos serão criados.

- `compartment_id`: O OCID do compartimento onde os recursos serão criados. Um compartimento é um espaço de nomes que contém todos os seus recursos na Oracle Cloud Infrastructure.

Para obter o compartment_id na Oracle Cloud Infrastructure (OCI), siga os passos abaixo:

1. Faça login na Console da Oracle Cloud Infrastructure.
2. No menu à esquerda, clique em "Identity & Security" e depois em "Compartments".
3. Na página "Compartments", você verá uma lista de todos os compartimentos na sua tenancy.
4. Localize o compartimento do qual você deseja obter o OCID e clique no nome do compartimento.
5. Na página de detalhes do compartimento, você encontrará o OCID do compartimento listado na seção "Compartment Information".

- `vcn_cidr_block`: O bloco CIDR para a Virtual Cloud Network (VCN). Este bloco define o espaço de endereços IP para a VCN.

- `vcn_dns_label`: O rótulo DNS para a VCN. Este rótulo é usado para formar o nome de domínio para os recursos na VCN.

- `vcn_display_name`: O nome de exibição para a VCN.

- `subnet_cidr_block`: O bloco CIDR para a sub-rede dentro da VCN. Este bloco define o espaço de endereços IP para a sub-rede.

- `subnet_display_name`: O nome de exibição para a sub-rede.

- `subnet_dns_label`: O rótulo DNS para a sub-rede. Este rótulo é usado para formar o nome de domínio para os recursos na sub-rede.

- `oke_pods_cidr`: O bloco CIDR para os pods no cluster Kubernetes. Este bloco define o espaço de endereços IP para os pods.

- `oke_services_cidr`: O bloco CIDR para os serviços no cluster Kubernetes. Este bloco define o espaço de endereços IP para os serviços.

- `node_shape`: O formato dos nós no cluster Kubernetes. Este formato define as características de hardware dos nós, como a quantidade de CPU e memória.

- `node_image_id`: O OCID da imagem que será usada para lançar os nós do cluster Kubernetes.

Esta imagem deve ter a versão k8s igual com a versão kubernetes_version.
Você pode consultar as imagens a partir deste comando:

```
oci compute image list --compartment-id <compartment-id>
```

- `ssh_public_key`: A chave pública SSH usada para acessar os nós.

- `node_pool_size`: O número de nós no node pool do cluster Kubernetes.

- `availability_domain`: O Availability Domain onde os nós do cluster Kubernetes serão criados.

- `kubernetes_version`: A versão do kubernetes suportada pela OCI

As versões atualmente suportadas são:

- v1.26.2
- v1.26.7
- v1.27.2
- v1.27.10
- v1.28.2
- v1.29.1

Você pode obter a lista atualizada com comando:

```
oci ce node-pool-options get --node-pool-option-id all
```

Para obter o availability_domain usando a linha de comando OCI, você pode usar o comando oci iam availability-domain list. Este comando lista todos os domínios de disponibilidade na sua tenancy.

```
oci iam availability-domain list --compartment-id=<your_compartment_id>
```

Você pode definir essas variáveis no arquivo `terraform.tfvars` ou passá-las como argumentos de linha de comando ao executar `terraform apply`.

## Como conectar no cluster:

Para se conectar ao cluster Kubernetes, você precisará do kubectl, a ferramenta de linha de comando do Kubernetes, e do arquivo de configuração do cluster (kubeconfig).

Instale o kubectl se ainda não o tiver. No macOS, você pode usar o Homebrew:
Obtenha o arquivo de configuração do cluster (kubeconfig). Se você estiver usando o Oracle Cloud Infrastructure (OCI), você pode obter o kubeconfig usando o comando oci ce cluster create-kubeconfig. Aqui está um exemplo:

```
oci ce cluster create-kubeconfig --cluster-id <cluster-id> --file $HOME/.kube/config --region <region> --token-version 2.0.0
```

Renomeie o contexto para facilitar a identifição do cluster, se você trabalha com mais de um cluster:

```
CURRENT_CONTEXT=$(kubectl config current-context)
kubectl config rename-context $CURRENT_CONTEXT PostoAki-k8s
```

## Como criar secrets de autentificação no OKE container registry:

Definir o auth token do usuário com username docker-registry.
Altere o namespace para o namespace que vai utilizar a secret.
```
kubectl -n api create secret docker-registry docker-registry --docker-server=gru.ocir.io --docker-username=grbvypj1mx3p/docker-registry --docker-password='<auth-token>'
```

Passo 2: Converta o conteúdo do arquivo docker-secret.json para base64

```
base64 -i k8s/api/docker-secret.json -o docker-secret.base64
```

Passo 3: Atualize o arquivo secrets.yaml
Abra o arquivo docker-secret.base64 e copie o conteúdo gerado
Substitua <base64-encoded-docker-config-json> no arquivo secrets.yaml pelo conteúdo copiado

Passo 4: Crie a secret no Kubernetes
kubectl apply -f k8s/api/secrets.yaml

## Configure o autoscale

Faça log-in na Console.
Crie um novo grupo dinâmico no nível do compartimento contendo os nós de trabalho (instâncias de computação) no cluster:

Abra o menu de navegação e clique em Identidade e Segurança. Em Identidade, clique em Domínios. Em domínio de identidades, clique em Grupos dinâmicos.
Selecione o compartimento que contém o cluster.
Siga as instruções em Para criar um grupo dinâmico e dê um nome ao grupo dinâmico (por exemplo, acme-oke-cluster-autoscaler-dyn-grp).
Digite uma regra que inclua os nós de trabalho no compartimento:

```
ALL {instance.compartment.id = 'ocid1.compartment.oc1..aaaaaaaaovof4p4uveta2pmm3wb25ubk3tfwqrtarimaq2apoabgbprnmmoa'}
```

Crie uma política para permitir que os nós de trabalho gerenciem pools de nós:

Abra o menu de navegação e clique em Identidade e Segurança. Em Identidade, clique em Políticas.
Siga as instruções em Para criar uma política e dê a ela um nome (por exemplo, acme-oke-cluster-autoscaler-dyn-grp-policy).
Digite uma instrução de política para permitir que os nós de trabalho gerenciem os pools de nós (com outras instruções de política relacionadas à inicialização dos nós de trabalho):

```
Allow dynamic-group OracleIdentityCloudService/acme-oke-cluster-autoscaler-dyn-grp to manage cluster-node-pools in compartment OKE
Allow dynamic-group OracleIdentityCloudService/acme-oke-cluster-autoscaler-dyn-grp to manage instance-family in compartment OKE
Allow dynamic-group OracleIdentityCloudService/acme-oke-cluster-autoscaler-dyn-grp to use subnets in compartment OKE
Allow dynamic-group OracleIdentityCloudService/acme-oke-cluster-autoscaler-dyn-grp to read virtual-network-family in compartment OKE
Allow dynamic-group OracleIdentityCloudService/acme-oke-cluster-autoscaler-dyn-grp to use vnics in compartment OKE
Allow dynamic-group OracleIdentityCloudService/acme-oke-cluster-autoscaler-dyn-grp to inspect compartments in compartment OKE
```

No arquivo `k8s/autoscale/cluster-autoscale.yaml` especifique cada um dos pools de nós do cluster que você deseja que o Kubernetes Cluster Autoscaler gerencie

Se quiser que o Kubernetes Cluster Autoscaler gerencie um segundo pool de nós no cluster, anexe os detalhes apropriados do segundo pool de nós ao valor do parâmetro nodes, separado por virgulas.

Instale o metrics server:

```
helm upgrade "metrics-server" "metrics-server/metrics-server" \
  --atomic \
  --create-namespace \
  --install \
  --timeout 8m0s \
  --namespace "metrics-server" \
  --values "k8s/monitoring/metrics-server/values.yml" \
  --version "3.11.0"
```

Aplique o Cluster Autoscaler no cluster:

```
kubectl apply -f k8s/autoscale/cluster-autoscale.yaml
```

Instale o Blackbox Exporter

```
helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
helm repo update
helm install blackbox-exporter prometheus-community/prometheus-blackbox-exporter \
  --namespace monitoring \
  --set serviceMonitor.enabled=true \
  --set serviceMonitor.namespace=monitoring
```

Configurar o ServiceMonitor para Health Checks

```
kubectl apply -f k8s/monitoring/service-monitor/servicemonitor.yaml
```

Para configurar urls de services que precisa monitorar edite o arquivo k8s/monitoring/service-monitor/values.yaml adicionando a url do healthchek:

```
vim k8s/monitoring/service-monitor/values.yaml
```

Agora atualize o prometheus:

```
helm upgrade prometheus prometheus-community/kube-prometheus-stack \
  -n monitoring \
  -f k8s/monitoring/service-monitor/values.yaml
```

## Faça as configurações de DNS e proxy

Aumente o tamanho de payload permitido no arquivo de configuração do nginx

```bash
kubectl edit configmap -n ingress-nginx ingress-nginx-controller
``

Altere o ConfigMap adicionando estas configurações na seção `data`:

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: ingress-nginx-controller
  namespace: ingress-nginx
  # ... resto dos metadados ...
data:
  proxy-body-size: "20m" # tamanho máximo do corpo da solicitação
  client-body-buffer-size: "20m" # tamanho máximo do buffer do corpo do cliente
```

```
kubectl rollout restart deployment ingress-nginx-controller  -n ingress-nginx
````
