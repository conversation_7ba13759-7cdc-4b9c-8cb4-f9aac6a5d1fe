# Instruções para Implementação do Backup PostgreSQL

## Resumo do que foi criado

✅ **Usuário Oracle Cloud**: `postgres-backup-user`
✅ **Grupo**: `postgres-backup-group`  
✅ **Política de acesso**: Permissão de escrita no bucket `postgres-backups`
✅ **Chaves de API**: Configuradas para autenticação
✅ **Script de sincronização**: Backup recursivo da pasta `/postgresql/pgdumps`
✅ **Script de deployment**: Instalação automática no servidor

## Arquivos Criados

1. **postgres-backup-sync.sh** - Script principal de sincronização
2. **postgres-backup-oci-config** - Configuração do OCI CLI
3. **postgres-backup-user-key.pem** - Chave privada do usuário
4. **postgres-backup-deploy.sh** - Script de instalação no servidor
5. **test-backup-upload.sh** - Script de teste local

## Informações do Usuário Oracle Cloud

- **Nome**: `postgres-backup-user`
- **OCID**: `ocid1.user.oc1..aaaaaaaa4e2pyiopkgvg7v3le52ndwu2mzz2gpr27k43dheskacjptvwhxca`
- **Grupo**: `postgres-backup-group`
- **Bucket**: `postgres-backups`
- **Compartment**: `OKE`
- **Namespace**: `grbvypj1mx3p`

## Implementação no Servidor

### 1. Copiar arquivos para o servidor

```bash
# Copie estes arquivos para o servidor:
scp postgres-backup-sync.sh  postoaki-vm-postgres-1:/tmp/
scp postgres-backup-oci-config  postoaki-vm-postgres-1:/tmp/
scp postgres-backup-user-key.pem  postoaki-vm-postgres-1:/tmp/
scp postgres-backup-deploy.sh  postoaki-vm-postgres-1:/tmp/
```

### 2. Executar instalação no servidor

```bash
# No servidor, execute como root:
cd /tmp
chmod +x postgres-backup-deploy.sh
./postgres-backup-deploy.sh
```

### 3. Testar configuração

```bash
# Teste a configuração:
/opt/postgres-backup/test-backup.sh

# Teste o backup (modo dry-run):
/opt/postgres-backup/postgres-backup-sync.sh --dry-run

# Execute backup real:
/opt/postgres-backup/postgres-backup-sync.sh
```

## Recursos do Script

### Funcionalidades principais:
- ✅ Sincronização recursiva da pasta `/postgresql/pgdumps`
- ✅ Verifica se arquivos já existem antes de fazer upload
- ✅ Limpeza automática de arquivos antigos (30 dias por padrão)
- ✅ Logs detalhados
- ✅ Modo teste (dry-run)
- ✅ Monitoramento e alertas
- ✅ Estatísticas de sincronização

### Comandos úteis:
```bash
# Executar backup manualmente
/opt/postgres-backup/postgres-backup-sync.sh

# Modo teste (não faz upload)
/opt/postgres-backup/postgres-backup-sync.sh --dry-run

# Listar arquivos no bucket
/opt/postgres-backup/postgres-backup-sync.sh --list-only

# Limpar arquivos antigos
/opt/postgres-backup/postgres-backup-sync.sh --cleanup-only

# Mostrar estatísticas
/opt/postgres-backup/postgres-backup-sync.sh --stats

# Monitorar backups
/opt/postgres-backup/monitor-backup.sh
```

## Configuração do Cron

O backup está configurado para executar diariamente às 02:00:
```bash
# Verificar cron do usuário postgres
crontab -u postgres -l

# Editar se necessário
crontab -u postgres -e
```

## Logs

- **Log principal**: `/var/log/postgres-backup/postgres-backup-sync.log`
- **Log do cron**: `/var/log/postgres-backup/cron.log`

## Personalização

Para alterar configurações, edite o script principal:
```bash
nano /opt/postgres-backup/postgres-backup-sync.sh
```

### Variáveis importantes:
- `BACKUP_BASE_DIR`: Diretório dos backups (padrão: `/postgresql/pgdumps`)
- `RETENTION_DAYS`: Dias de retenção (padrão: `30`)
- `BUCKET_NAME`: Nome do bucket (`postgres-backups`)

## Monitoramento

O script de monitoramento pode ser configurado para:
- Verificar se o backup executou recentemente
- Detectar erros na sincronização
- Enviar alertas via syslog ou email

Para configurar alertas por email, edite:
```bash
nano /opt/postgres-backup/monitor-backup.sh
```

## Segurança

- ✅ Chaves privadas com permissões 600
- ✅ Configuração em diretório protegido (`/etc/postgres-backup/`)
- ✅ Usuário com permissões mínimas necessárias
- ✅ Logs detalhados para auditoria

## Estrutura no Bucket

Os arquivos serão organizados assim:
```
postgres-backups/
├── pgdumps/
│   ├── database1/
│   │   ├── backup_20250715.sql
│   │   └── backup_20250716.sql
│   └── database2/
│       ├── backup_20250715.sql
│       └── backup_20250716.sql
└── test-backups/
    └── test-backup-20250714_215018.sql
```

## Troubleshooting

### OCI CLI não instalado
```bash
/opt/postgres-backup/install-oci-cli.sh
```

### Verificar conectividade
```bash
/opt/postgres-backup/postgres-backup-sync.sh --dry-run
```

### Verificar permissões
```bash
ls -la /etc/postgres-backup/
```

### Logs de erro
```bash
tail -f /var/log/postgres-backup/postgres-backup-sync.log
```

## Backup e Restore da Configuração

### Backup da configuração
```bash
tar -czf postgres-backup-config.tar.gz /etc/postgres-backup/ /opt/postgres-backup/
```

### Restore da configuração
```bash
tar -xzf postgres-backup-config.tar.gz -C /
```

## Contato

Para suporte ou dúvidas sobre a implementação, entre em contato com o administrador do sistema.
