#!/bin/bash

# Script de sincronização de backups PostgreSQL para Oracle Cloud Storage
# Autor: <PERSON><PERSON>
# Data: $(date)
# Versão: 1.0

# Configuração do ambiente
export OCI_CONFIG_FILE="/path/to/postgres-backup-oci-config"
export BUCKET_NAME="postgres-backups"
export NAMESPACE="grbvypj1mx3p"
export COMPARTMENT_ID="ocid1.compartment.oc1..aaaaaaaaovof4p4uveta2pmm3wb25ubk3tfwqrtarimaq2apoabgbprnmmoa"

# Suprime warnings do OCI CLI
export SUPPRESS_LABEL_WARNING=True
export OCI_CLI_SUPPRESS_FILE_PERMISSIONS_WARNING=True

# Configurações do backup
BACKUP_BASE_DIR="/postgresql/pgdumps"
LOG_DIR="/var/log/postgres-backup"
LOG_FILE="${LOG_DIR}/postgres-backup-sync.log"
RETENTION_DAYS=30
DRY_RUN=false

# Função para logging
log() {
    local level="$1"
    local message="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [$level] $message" | tee -a "$LOG_FILE"
}

# Função para verificar se o arquivo já existe no bucket
object_exists() {
    local object_name="$1"
    
    oci os object head \
        --config-file "$OCI_CONFIG_FILE" \
        --bucket-name "$BUCKET_NAME" \
        --namespace "$NAMESPACE" \
        --name "$object_name" \
        --output json > /dev/null 2>&1
    
    return $?
}

# Função para fazer upload de um arquivo
upload_file() {
    local file_path="$1"
    local object_name="$2"
    
    if [ ! -f "$file_path" ]; then
        log "ERROR" "Arquivo não encontrado: $file_path"
        return 1
    fi
    
    # Verifica se o arquivo já existe no bucket
    if object_exists "$object_name"; then
        log "INFO" "Arquivo já existe no bucket: $object_name - Pulando..."
        return 0
    fi
    
    if [ "$DRY_RUN" = true ]; then
        log "INFO" "[DRY RUN] Faria upload: $file_path -> $object_name"
        return 0
    fi
    
    log "INFO" "Fazendo upload: $file_path -> $object_name"
    
    # Faz o upload do arquivo
    oci os object put \
        --config-file "$OCI_CONFIG_FILE" \
        --bucket-name "$BUCKET_NAME" \
        --namespace "$NAMESPACE" \
        --file "$file_path" \
        --name "$object_name" \
        --force > /dev/null 2>&1
    
    if [ $? -eq 0 ]; then
        log "SUCCESS" "Upload concluído: $object_name"
        return 0
    else
        log "ERROR" "Falha no upload: $object_name"
        return 1
    fi
}

# Função para sincronizar diretório
sync_directory() {
    local source_dir="$1"
    local prefix="$2"
    
    if [ ! -d "$source_dir" ]; then
        log "ERROR" "Diretório não encontrado: $source_dir"
        return 1
    fi
    
    log "INFO" "Sincronizando diretório: $source_dir"
    
    # Encontra todos os arquivos no diretório recursivamente
    find "$source_dir" -type f | while read -r file; do
        # Calcula o caminho relativo
        relative_path=$(realpath --relative-to="$BACKUP_BASE_DIR" "$file")
        
        # Cria o nome do objeto no bucket
        object_name="${prefix}${relative_path}"
        
        # Faz o upload do arquivo
        upload_file "$file" "$object_name"
    done
}

# Função para listar arquivos no bucket
list_bucket_objects() {
    log "INFO" "Listando objetos no bucket $BUCKET_NAME..."
    
    oci os object list \
        --config-file "$OCI_CONFIG_FILE" \
        --bucket-name "$BUCKET_NAME" \
        --namespace "$NAMESPACE" \
        --output table
}

# Função para limpar arquivos antigos do bucket
cleanup_old_files() {
    local retention_days="$1"
    
    if [ "$DRY_RUN" = true ]; then
        log "INFO" "[DRY RUN] Faria limpeza de arquivos mais antigos que $retention_days dias"
        return 0
    fi
    
    log "INFO" "Removendo arquivos mais antigos que $retention_days dias..."
    
    # Calcula a data limite
    local cutoff_date=$(date -d "-$retention_days days" '+%Y-%m-%d')
    
    # Lista objetos e filtra por data
    oci os object list \
        --config-file "$OCI_CONFIG_FILE" \
        --bucket-name "$BUCKET_NAME" \
        --namespace "$NAMESPACE" \
        --output json | \
    jq -r --arg cutoff "$cutoff_date" '.data[] | select(.["time-created"] < $cutoff) | .name' | \
    while read -r object_name; do
        log "INFO" "Removendo arquivo antigo: $object_name"
        
        oci os object delete \
            --config-file "$OCI_CONFIG_FILE" \
            --bucket-name "$BUCKET_NAME" \
            --namespace "$NAMESPACE" \
            --name "$object_name" \
            --force > /dev/null 2>&1
        
        if [ $? -eq 0 ]; then
            log "SUCCESS" "Arquivo removido: $object_name"
        else
            log "ERROR" "Falha ao remover: $object_name"
        fi
    done
}

# Função para verificar dependências
check_dependencies() {
    local missing_deps=()
    
    # Verifica se o OCI CLI está instalado
    if ! command -v oci &> /dev/null; then
        missing_deps+=("oci-cli")
    fi
    
    # Verifica se o jq está instalado
    if ! command -v jq &> /dev/null; then
        missing_deps+=("jq")
    fi
    
    # Verifica se o arquivo de configuração existe
    if [ ! -f "$OCI_CONFIG_FILE" ]; then
        missing_deps+=("config-file:$OCI_CONFIG_FILE")
    fi
    
    # Verifica se o diretório de backup existe
    if [ ! -d "$BACKUP_BASE_DIR" ]; then
        missing_deps+=("backup-directory:$BACKUP_BASE_DIR")
    fi
    
    if [ ${#missing_deps[@]} -gt 0 ]; then
        log "ERROR" "Dependências não atendidas: ${missing_deps[*]}"
        return 1
    fi
    
    return 0
}

# Função para mostrar estatísticas
show_statistics() {
    log "INFO" "=== Estatísticas de Sincronização ==="
    
    # Conta arquivos locais
    local local_files=$(find "$BACKUP_BASE_DIR" -type f | wc -l)
    log "INFO" "Arquivos locais: $local_files"
    
    # Conta arquivos no bucket
    local bucket_files=$(oci os object list \
        --config-file "$OCI_CONFIG_FILE" \
        --bucket-name "$BUCKET_NAME" \
        --namespace "$NAMESPACE" \
        --output json | jq '.data | length')
    log "INFO" "Arquivos no bucket: $bucket_files"
    
    # Calcula tamanho total dos arquivos locais
    local total_size=$(find "$BACKUP_BASE_DIR" -type f -exec stat -c%s {} + | awk '{sum+=$1} END {print sum}')
    local total_size_gb=$(echo "scale=2; $total_size / 1024 / 1024 / 1024" | bc)
    log "INFO" "Tamanho total local: ${total_size_gb}GB"
}

# Função para mostrar ajuda
show_help() {
    cat << EOF
Uso: $0 [opções]

Opções:
    -h, --help          Mostra esta ajuda
    -d, --dry-run       Executa em modo teste (não faz upload)
    -c, --config FILE   Arquivo de configuração OCI (padrão: $OCI_CONFIG_FILE)
    -b, --base-dir DIR  Diretório base dos backups (padrão: $BACKUP_BASE_DIR)
    -r, --retention N   Dias de retenção para limpeza (padrão: $RETENTION_DAYS)
    -l, --log-file FILE Arquivo de log (padrão: $LOG_FILE)
    --list-only         Apenas lista arquivos do bucket
    --cleanup-only      Apenas executa limpeza de arquivos antigos
    --stats             Mostra estatísticas

Exemplos:
    $0                          # Sincronização normal
    $0 --dry-run               # Teste sem fazer upload
    $0 --retention 60          # Manter arquivos por 60 dias
    $0 --list-only             # Apenas listar arquivos
    $0 --cleanup-only          # Apenas limpar arquivos antigos
EOF
}

# Função principal
main() {
    # Parse dos argumentos
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -d|--dry-run)
                DRY_RUN=true
                shift
                ;;
            -c|--config)
                OCI_CONFIG_FILE="$2"
                shift 2
                ;;
            -b|--base-dir)
                BACKUP_BASE_DIR="$2"
                shift 2
                ;;
            -r|--retention)
                RETENTION_DAYS="$2"
                shift 2
                ;;
            -l|--log-file)
                LOG_FILE="$2"
                shift 2
                ;;
            --list-only)
                list_bucket_objects
                exit 0
                ;;
            --cleanup-only)
                cleanup_old_files "$RETENTION_DAYS"
                exit 0
                ;;
            --stats)
                show_statistics
                exit 0
                ;;
            *)
                log "ERROR" "Opção desconhecida: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # Cria diretório de log se não existir
    mkdir -p "$(dirname "$LOG_FILE")"
    
    log "INFO" "=== Iniciando sincronização de backups PostgreSQL ==="
    log "INFO" "Configuração:"
    log "INFO" "  - Diretório base: $BACKUP_BASE_DIR"
    log "INFO" "  - Bucket: $BUCKET_NAME"
    log "INFO" "  - Namespace: $NAMESPACE"
    log "INFO" "  - Arquivo de log: $LOG_FILE"
    log "INFO" "  - Retenção: $RETENTION_DAYS dias"
    log "INFO" "  - Modo teste: $DRY_RUN"
    
    # Verifica dependências
    if ! check_dependencies; then
        log "ERROR" "Falha na verificação de dependências"
        exit 1
    fi
    
    # Testa conectividade com o bucket
    log "INFO" "Testando conectividade com o bucket..."
    if ! oci os bucket get \
        --config-file "$OCI_CONFIG_FILE" \
        --bucket-name "$BUCKET_NAME" \
        --namespace "$NAMESPACE" > /dev/null 2>&1; then
        log "ERROR" "Não foi possível conectar ao bucket $BUCKET_NAME"
        exit 1
    fi
    log "SUCCESS" "Conectividade com o bucket OK"
    
    # Sincroniza arquivos
    sync_directory "$BACKUP_BASE_DIR" "pgdumps/"
    
    # Mostra estatísticas
    show_statistics
    
    # Limpa arquivos antigos
    if [ "$RETENTION_DAYS" -gt 0 ]; then
        cleanup_old_files "$RETENTION_DAYS"
    fi
    
    log "INFO" "=== Sincronização concluída ==="
}

# Executa função principal
main "$@"
