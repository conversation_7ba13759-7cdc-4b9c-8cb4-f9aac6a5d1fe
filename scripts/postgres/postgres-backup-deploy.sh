#!/bin/bash

# Script de deployment para configuração de backup PostgreSQL
# Autor: <PERSON><PERSON>
# Data: $(date)

# Configurações
SCRIPT_DIR="/opt/postgres-backup"
CONFIG_DIR="/etc/postgres-backup"
LOG_DIR="/var/log/postgres-backup"
CRON_USER="ubuntu"

# Função para logging
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Função para instalar dependências
install_dependencies() {
    log "Atualizando repositórios..."
    apt-get update
    
    log "Instalando dependências..."
    
    # Instala jq, bc e curl
    apt-get install -y jq bc curl python3-pip
    
    # Instala o OCI CLI se não estiver instalado
    if ! command -v oci &> /dev/null; then
        log "Instalando OCI CLI..."
        pip3 install oci-cli
    fi
    
    log "Dependências instaladas com sucesso"
}

# Função para criar diretórios
create_directories() {
    log "Criando diretórios..."
    
    mkdir -p "$SCRIPT_DIR"
    mkdir -p "$CONFIG_DIR"
    mkdir -p "$LOG_DIR"
    
    # Ajusta permissões
    chmod 755 "$SCRIPT_DIR"
    chmod 700 "$CONFIG_DIR"
    chmod 755 "$LOG_DIR"
}

# Função para copiar arquivos
copy_files() {
    log "Copiando arquivos de configuração..."
    
    # Copia o script principal
    cp postgres-backup-sync.sh "$SCRIPT_DIR/"
    chmod +x "$SCRIPT_DIR/postgres-backup-sync.sh"
    
    # Copia arquivos de configuração
    cp postgres-backup-oci-config "$CONFIG_DIR/"
    cp postgres-backup-user-key.pem "$CONFIG_DIR/"
    
    # Ajusta permissões dos arquivos sensíveis
    chmod 600 "$CONFIG_DIR/postgres-backup-oci-config"
    chmod 600 "$CONFIG_DIR/postgres-backup-user-key.pem"
    
    # Atualiza o caminho no script
    sed -i "s|/path/to/postgres-backup-oci-config|$CONFIG_DIR/postgres-backup-oci-config|g" "$SCRIPT_DIR/postgres-backup-sync.sh"
}

# Função para configurar cron
setup_cron() {
    log "Configurando cron job..."
    
    # Cria o cron job para executar diariamente às 02:00
    cat > "/tmp/postgres-backup-cron" << EOF
# Backup PostgreSQL para Oracle Cloud Storage
# Executa diariamente às 02:00
0 2 * * * $SCRIPT_DIR/postgres-backup-sync.sh >> $LOG_DIR/cron.log 2>&1
EOF
    
    # Instala o cron job
    crontab -u "$CRON_USER" "/tmp/postgres-backup-cron"
    rm -f "/tmp/postgres-backup-cron"
    
    log "Cron job configurado para usuário $CRON_USER"
}

# Função para criar script de teste
create_test_script() {
    log "Criando script de teste..."
    
    cat > "$SCRIPT_DIR/test-backup.sh" << 'EOF'
#!/bin/bash

# Script de teste para verificar a configuração
SCRIPT_DIR="/opt/postgres-backup"
CONFIG_DIR="/etc/postgres-backup"

echo "=== Teste de Configuração de Backup PostgreSQL ==="

# Verifica se os arquivos existem
echo "Verificando arquivos..."
if [ -f "$SCRIPT_DIR/postgres-backup-sync.sh" ]; then
    echo "✓ Script principal encontrado"
else
    echo "✗ Script principal não encontrado"
    exit 1
fi

if [ -f "$CONFIG_DIR/postgres-backup-oci-config" ]; then
    echo "✓ Arquivo de configuração encontrado"
else
    echo "✗ Arquivo de configuração não encontrado"
    exit 1
fi

if [ -f "$CONFIG_DIR/postgres-backup-user-key.pem" ]; then
    echo "✓ Chave privada encontrada"
else
    echo "✗ Chave privada não encontrada"
    exit 1
fi

# Verifica dependências
echo "Verificando dependências..."
if command -v oci &> /dev/null; then
    echo "✓ OCI CLI instalado"
else
    echo "✗ OCI CLI não instalado"
    exit 1
fi

if command -v jq &> /dev/null; then
    echo "✓ jq instalado"
else
    echo "✗ jq não instalado"
    exit 1
fi

if command -v bc &> /dev/null; then
    echo "✓ bc instalado"
else
    echo "✗ bc não instalado"
    exit 1
fi

# Testa conectividade
echo "Testando conectividade..."
if "$SCRIPT_DIR/postgres-backup-sync.sh" --dry-run; then
    echo "✓ Teste de conectividade OK"
else
    echo "✗ Falha no teste de conectividade"
    exit 1
fi

echo "=== Configuração OK ==="
EOF

    chmod +x "$SCRIPT_DIR/test-backup.sh"
}

# Função para criar script de monitoramento
create_monitoring_script() {
    log "Criando script de monitoramento..."
    
    cat > "$SCRIPT_DIR/monitor-backup.sh" << 'EOF'
#!/bin/bash

# Script de monitoramento de backup
SCRIPT_DIR="/opt/postgres-backup"
LOG_DIR="/var/log/postgres-backup"
LOG_FILE="$LOG_DIR/postgres-backup-sync.log"

# Função para enviar notificação (personalizar conforme necessário)
send_notification() {
    local message="$1"
    local priority="$2"
    
    # Exemplo: enviar para syslog
    logger -t postgres-backup "$priority: $message"
    
    # Exemplo: enviar email (se configurado)
    # echo "$message" | mail -s "PostgreSQL Backup Alert" <EMAIL>
}

# Verifica se o backup executou nas últimas 25 horas
check_last_backup() {
    if [ ! -f "$LOG_FILE" ]; then
        send_notification "Arquivo de log não encontrado" "CRITICAL"
        return 1
    fi
    
    # Procura pela última execução
    last_run=$(grep "Iniciando sincronização" "$LOG_FILE" | tail -1 | awk '{print $1, $2}')
    
    if [ -z "$last_run" ]; then
        send_notification "Nenhuma execução de backup encontrada" "CRITICAL"
        return 1
    fi
    
    # Converte para timestamp
    last_timestamp=$(date -d "$last_run" +%s)
    current_timestamp=$(date +%s)
    
    # Calcula diferença em horas
    hours_diff=$(( (current_timestamp - last_timestamp) / 3600 ))
    
    if [ "$hours_diff" -gt 25 ]; then
        send_notification "Backup não executado há $hours_diff horas" "CRITICAL"
        return 1
    fi
    
    return 0
}

# Verifica se houve erros na última execução
check_errors() {
    if [ ! -f "$LOG_FILE" ]; then
        return 1
    fi
    
    # Procura por erros nas últimas 24 horas
    errors=$(tail -1000 "$LOG_FILE" | grep -c "ERROR")
    
    if [ "$errors" -gt 0 ]; then
        send_notification "Encontrados $errors erros na sincronização" "WARNING"
        return 1
    fi
    
    return 0
}

# Executa verificações
echo "=== Monitoramento de Backup PostgreSQL ==="
echo "Verificando último backup..."
if check_last_backup; then
    echo "✓ Backup executado recentemente"
else
    echo "✗ Problema com execução do backup"
fi

echo "Verificando erros..."
if check_errors; then
    echo "✓ Nenhum erro encontrado"
else
    echo "✗ Erros encontrados na sincronização"
fi

echo "=== Monitoramento concluído ==="
EOF

    chmod +x "$SCRIPT_DIR/monitor-backup.sh"
}

# Função para criar script de instalação manual do OCI CLI
create_oci_install_script() {
    log "Criando script de instalação manual do OCI CLI..."
    
    cat > "$SCRIPT_DIR/install-oci-cli.sh" << 'EOF'
#!/bin/bash

# Script para instalação manual do OCI CLI se pip3 falhar

echo "=== Instalação Manual do OCI CLI ==="

# Método 1: Usando pip3
echo "Tentando instalar via pip3..."
if pip3 install oci-cli; then
    echo "✓ OCI CLI instalado via pip3"
    exit 0
fi

# Método 2: Script de instalação oficial
echo "Tentando instalar via script oficial..."
curl -L https://raw.githubusercontent.com/oracle/oci-cli/master/scripts/install/install.sh | bash

# Adiciona ao PATH
if [ -f "$HOME/bin/oci" ]; then
    echo "export PATH=\$PATH:\$HOME/bin" >> ~/.bashrc
    export PATH=$PATH:$HOME/bin
    echo "✓ OCI CLI instalado via script oficial"
else
    echo "✗ Falha na instalação do OCI CLI"
    exit 1
fi
EOF

    chmod +x "$SCRIPT_DIR/install-oci-cli.sh"
}

# Função para criar arquivo README com instruções
create_readme() {
    log "Criando arquivo README..."
    
    cat > "$SCRIPT_DIR/README.md" << 'EOF'
# Backup PostgreSQL para Oracle Cloud Storage

## Arquivos Instalados

- `/opt/postgres-backup/postgres-backup-sync.sh` - Script principal de sincronização
- `/opt/postgres-backup/test-backup.sh` - Script de teste da configuração
- `/opt/postgres-backup/monitor-backup.sh` - Script de monitoramento
- `/opt/postgres-backup/install-oci-cli.sh` - Instalação manual do OCI CLI
- `/etc/postgres-backup/postgres-backup-oci-config` - Configuração do OCI CLI
- `/etc/postgres-backup/postgres-backup-user-key.pem` - Chave privada do usuário

## Uso

### Testar configuração
```bash
/opt/postgres-backup/test-backup.sh
```

### Executar backup manualmente
```bash
/opt/postgres-backup/postgres-backup-sync.sh
```

### Executar em modo teste (dry-run)
```bash
/opt/postgres-backup/postgres-backup-sync.sh --dry-run
```

### Monitorar backups
```bash
/opt/postgres-backup/monitor-backup.sh
```

### Listar arquivos no bucket
```bash
/opt/postgres-backup/postgres-backup-sync.sh --list-only
```

### Limpar arquivos antigos
```bash
/opt/postgres-backup/postgres-backup-sync.sh --cleanup-only
```

### Mostrar estatísticas
```bash
/opt/postgres-backup/postgres-backup-sync.sh --stats
```

## Configuração do Cron

O backup está configurado para executar diariamente às 02:00 através do cron do usuário postgres.

Para verificar:
```bash
crontab -u postgres -l
```

Para editar:
```bash
crontab -u postgres -e
```

## Logs

Os logs são salvos em:
- `/var/log/postgres-backup/postgres-backup-sync.log` - Log principal
- `/var/log/postgres-backup/cron.log` - Log do cron

## Troubleshooting

### OCI CLI não instalado
Execute:
```bash
/opt/postgres-backup/install-oci-cli.sh
```

### Verificar conectividade
```bash
/opt/postgres-backup/postgres-backup-sync.sh --dry-run
```

### Verificar permissões
```bash
ls -la /etc/postgres-backup/
```

As permissões devem ser:
- postgres-backup-oci-config: 600
- postgres-backup-user-key.pem: 600

## Configuração Personalizada

Para alterar configurações, edite o script principal:
```bash
nano /opt/postgres-backup/postgres-backup-sync.sh
```

Variáveis importantes:
- `BACKUP_BASE_DIR`: Diretório dos backups (padrão: /postgresql/pgdumps)
- `RETENTION_DAYS`: Dias de retenção (padrão: 30)
- `BUCKET_NAME`: Nome do bucket (postgres-backups)
EOF

    chmod 644 "$SCRIPT_DIR/README.md"
}

# Função principal
main() {
    log "=== Iniciando deployment de backup PostgreSQL ==="
    
    # Verifica se está sendo executado como root
    if [ "$EUID" -ne 0 ]; then
        log "Este script deve ser executado como root"
        exit 1
    fi
    
    # Verifica se os arquivos necessários existem
    if [ ! -f "postgres-backup-sync.sh" ] || [ ! -f "postgres-backup-oci-config" ] || [ ! -f "postgres-backup-user-key.pem" ]; then
        log "Arquivos necessários não encontrados no diretório atual"
        log "Necessário: postgres-backup-sync.sh, postgres-backup-oci-config, postgres-backup-user-key.pem"
        exit 1
    fi
    
    # Instala dependências
    install_dependencies
    
    # Cria diretórios
    create_directories
    
    # Copia arquivos
    copy_files
    
    # Configura cron
    setup_cron
    
    # Cria scripts auxiliares
    create_test_script
    create_monitoring_script
    create_oci_install_script
    create_readme
    
    log "=== Deployment concluído ==="
    log "Configuração instalada em: $SCRIPT_DIR"
    log "Arquivos de configuração em: $CONFIG_DIR"
    log "Logs em: $LOG_DIR"
    log ""
    log "Para testar a configuração, execute:"
    log "  $SCRIPT_DIR/test-backup.sh"
    log ""
    log "Para executar backup manualmente:"
    log "  $SCRIPT_DIR/postgres-backup-sync.sh"
    log ""
    log "Para monitorar backups:"
    log "  $SCRIPT_DIR/monitor-backup.sh"
    log ""
    log "Para mais informações, consulte:"
    log "  $SCRIPT_DIR/README.md"
}

# Executa função principal
main "$@"
