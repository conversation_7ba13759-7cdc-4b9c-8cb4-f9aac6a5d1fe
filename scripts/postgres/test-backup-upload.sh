#!/bin/bash

# Script de exemplo para testar o upload de backup para o bucket postgres-backups
# Configuração do ambiente

export SCRIPT_DIR="/opt/postgres-backup"
export CONFIG_DIR="/etc/postgres-backup"
export OCI_CONFIG_FILE="$CONFIG_DIR/postgres-backup-oci-config"
export BUCKET_NAME="postgres-backups"
export NAMESPACE="grbvypj1mx3p"
export COMPARTMENT_ID="ocid1.compartment.oc1..aaaaaaaaovof4p4uveta2pmm3wb25ubk3tfwqrtarimaq2apoabgbprnmmoa"

# Função para fazer upload de um arquivo
upload_backup() {
    local file_path="$1"
    local object_name="$2"
    
    if [ ! -f "$file_path" ]; then
        echo "Erro: Arquivo $file_path não encontrado"
        return 1
    fi
    
    echo "Fazendo upload do arquivo $file_path para o bucket $BUCKET_NAME..."
    
    oci os object put \
        --config-file "$OCI_CONFIG_FILE" \
        --bucket-name "$BUCKET_NAME" \
        --namespace "$NAMESPACE" \
        --file "$file_path" \
        --name "$object_name" \
        --force
    
    if [ $? -eq 0 ]; then
        echo "Upload realizado com sucesso!"
        return 0
    else
        echo "Erro no upload"
        return 1
    fi
}

# Função para listar objetos no bucket
list_backups() {
    echo "Listando backups no bucket $BUCKET_NAME..."
    
    oci os object list \
        --config-file "$OCI_CONFIG_FILE" \
        --bucket-name "$BUCKET_NAME" \
        --namespace "$NAMESPACE" \
        --output table
}

# Função para criar um backup de teste
create_test_backup() {
    local backup_file="test-backup-$(date +%Y%m%d_%H%M%S).sql"
    
    echo "-- Backup de teste criado em $(date)" > "$backup_file"
    echo "-- Este é um arquivo de teste para verificar o upload" >> "$backup_file"
    
    echo "Arquivo de teste criado: $backup_file" >&2
    echo "$backup_file"
}

# Exemplo de uso
echo "=== Teste de Upload de Backup PostgreSQL ==="
echo "Bucket: $BUCKET_NAME"
echo "Namespace: $NAMESPACE"
echo "Compartment: $COMPARTMENT_ID"
echo ""

# Criar arquivo de teste
test_file=$(create_test_backup)

# Fazer upload
upload_backup "$test_file" "test-backups/$test_file"

# Listar arquivos no bucket
list_backups

# Limpar arquivo de teste
rm -f "$test_file"
echo "Arquivo de teste removido."
