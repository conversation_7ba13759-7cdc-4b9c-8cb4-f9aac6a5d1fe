# MIGRAÇÃO - Notas Organizadas

## O QUE ESTÁ FALTANDO?
- **Homologação:** Não foi migrado: alterar para utilizar o banco da vm homologação
- **Sonarqube:** Não foi migrado: falta banco em produçao

## Servidor de Preço
- Rodar script:
  - Pausar o ambiente (docker stop tomcat-servico-preco)
  - Reiniciar Postg<PERSON> (docker restart postgres) e aguardar 10 segundos
  - Parar todas as sessões do banco:  
    `docker exec -it postgres psql -U postgres -d postgres -c "SELECT pg_cancel_backend(pid) FROM pg_stat_activity where datname = 'postoakiservico' order by 1 desc";`
  - Excluir o banco:  
    `docker exec -it postgres psql -U postgres -d postgres -c "DROP DATABASE IF EXISTS postoakiservico";`
  - Criar novo banco:  
    `docker exec -it postgres psql -U postgres -d postgres -c "CREATE DATABASE postoakiservico";`
  - Startar o ambiente (docker start tomcat-servico-preco)
- Pipeline Azure: [Link](https://dev.azure.com/postoaki/PostoAki/_build?definitionId=122)
- Após script: Enviar requisição para recadastrar empresas:  
  `curl --location 'http://apiservicopreco.postoaki.com.br/api/v1/suporte/sync'`

## Servidor Painel
- Configurar:
  - **wiki.postoaki.com.br:** Usando Docker.  
    Repositório: [Link](https://dev.azure.com/postoaki/PostoAki/_git/postoaki-requisitos-wiki)
  - **redirect.postoaki.com.br:**  
    Repositório: [Link](https://dev.azure.com/postoaki/PostoAki/_git/postoaki-redirect)

## Servidor Owncloud (Drive)
- Ajustes:
  - Migrar dns de ************* para lb01.postoaki.com.br
  - Configurar drive2.postoaki.com.br
  - Armazenamento de 4TB (storage separado)
  - Descarte de backup de documentação LGPD (não executa LGPD 100%)
- Tipos de usuário:
  - **postoaki:** Logs, banco de desenvolvimento
  - **backupprod:**  
    - Backup do banco de produção (22h15 --- 2h15)  
    - Exclusões:
      - Semanal: excluir backups completos após 7 dias às 2h15
      - Trimestral e anual: manter apenas alguns backups conforme regras
      - Total: exclusão total após 5 anos
  - **treinamento:** Vídeos da plataforma (Wiki)
- Containers:
  - nginx: Container ID ee90b774f688 (80 e 443)
  - Owncloud: Container ID cac0442ca4fd (8000->8080)
  - redis: Container ID ce2bc8c2df0b (porta 6379)
  - mariadb: Container ID 4e39852afd0d (porta 3306)

## Servidor Homologação
- Domínios:
  - gameficacaoweb.postoaki.com.br
  - painelgameficacao.postoaki.com.br
  - painelhomolog.postoaki.com.br
  - painelmaster.postoaki.com.br
  - painelvendamaster.postoaki.com.br
  - portainer.postoaki.com.br
  - sonarqube.postoaki.com.br
  - apigameficacao.postoaki.com.br
  - apihomolog.postoaki.com.br
  - apimaster.postoaki.com.br
- Containers:
  - **tomcat-gameficacao:** Container ID 8d8588590229 (0.0.0.0:8181->8080)
  - **Portainer:** Container ID c2ce5a0310cd (0.0.0.0:9001->9000)
  - **nginx:** Container ID 7796c6b3cee8 (80 e 443)
  - **PgBouncer:** Container ID e67585bfa3a8 (0.0.0.0:6432->6432)
  - **Tomcat Homologação:** Container ID c9e7da2d8da1 (0.0.0.0:8080->8080)
  - **Tomcat Master:** Container ID 4629456eeacf (0.0.0.0:8282->8080)
  - **Postgres:** Container ID 078024c0b924 (0.0.0.0:5433->5432)
  - **Sonarqube:** Container ID 4391ecb424a0 (0.0.0.0:9000->9000)
  - **Postgres (Outra instância):** Container ID 997c9639232c (0.0.0.0:5432->5432)

## QUEM PRECISA DE BACKUP?
- **Metabase:** Backup semanal
- **Tomcat Homologação:** Backup semanal

## DECIDIR SOBRE BANCO DE DADOS
- Opções:
  - 1 servidor com todos (sempre ativo; considerar Kubernetes ou VM)
  - Servidor dedicado para banco de dados (Produção)

## SCRIPT SERVIDOR PREÇO
1. Script de Preço:
   - Variáveis:
     - nomeDockerTomcat=tomcat-servico-preco
     - nomeBancoPostgres=postoakiservico
     - nomeDockerPostgres=postgres
   - Passos:
     - Pausar ambiente: `docker stop $nomeDockerTomcat`
     - Reiniciar Postgres: `docker restart $nomeDockerPostgres` + aguardar 10 segundos
     - Parar sessões do BD:
       ```
       docker exec -it $nomeDockerPostgres psql -U postgres -d postgres -c "SELECT pg_cancel_backend(pid) FROM pg_stat_activity where datname = '$nomeBancoPostgres' order by 1 desc";
       ```
     - Excluir banco:  
       ```
       docker exec -it $nomeDockerPostgres psql -U postgres -d postgres -c "DROP DATABASE IF EXISTS $nomeBancoPostgres";
       ```
     - Criar novo banco:  
       ```
       docker exec -it $nomeDockerPostgres psql -U postgres -d postgres -c "CREATE DATABASE $nomeBancoPostgres";
       ```
     - Startar ambiente: `docker start $nomeDockerTomcat`
2. URL para sincronizar empresas:  
   `http://apiservicopreco.postoaki.com.br/api/v1/suporte/sync`

## CHECKLIST RESUMIDO
- [X] Metabase: Banco de dados (Postgres) – FALTA migrar/configurar
- [X] Serviço Preço: Banco de dados (Postgres) – FALTA migrar/configurar
- [X] Site: Banco de dados (MySQL) – Não migrado
- [X] Servidor de Preço: Script executado e pipeline configurado
- [X] Servidor Painel: Configurar wiki e redirect pendentes
- [X] Servidor Owncloud (Drive): Ajustar drive, armazenamento e políticas de backup
- [ ] Servidor Homologação: Ajustar containers e backups
- [X] Decisão sobre Banco de Dados: Definir se é único ou dedicado
