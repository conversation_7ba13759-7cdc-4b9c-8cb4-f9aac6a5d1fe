import requests
from locust import HttpUser, TaskSet, task, between

class UserBehavior(TaskSet):

    def on_start(self):
        self.limpar_cache()
        self.obter_data_hora_atual()
        self.obter_token()
        self.realizar_login()

    def limpar_cache(self):
        self.client.get("/api/v1/suporte/limparcache")

    def obter_data_hora_atual(self):
        response = self.client.get("/api/v1/ping/json")
        dataAtual = response.json()

        from datetime import datetime

        dataAtual = datetime.now()
        self.horaFormatada = dataAtual.strftime("%H:%M:%S")
        self.dataFormatada = dataAtual.strftime("%d/%m/%Y")

    def obter_token(self):
        url = 'https://hml-api.postoaki.app/oauth/token'
        headers = {
            'X-Grupo-Empresa': '1a321181-41ff-2590-d4b9-28dd0a0956ba',
            'Authorization': 'Basic YXBwdjM6MzlkYWU5NmUzODkzNDM0NGQ4YWZhNDJkZWRmMjA4ZTY='
        }
        data = {
            'grant_type': 'password',
            'username': '<EMAIL>',
            'password': '2c2f4d175767c32fc4eab398a880c747'
        }

        response = requests.post(url, headers=headers, data=data)
        assert response.status_code == 200, f"Expected status code 200, but got {response.status_code}"

        jsonData = response.json()
        assert jsonData is not None, "Response JSON is null"
        assert 'access_token' in jsonData, "Access token not found in response"

        self.access_token = jsonData['access_token']

    def realizar_login(self):
        url = 'https://hml-api.postoaki.app/api/v3/auth/login'
        headers = {
            'X-Client-Name': 'app',
            'X-Grupo-Empresa': '1a321181-41ff-2590-d4b9-28dd0a0956ba',
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {self.access_token}'
        }
        data = {
            'username': '<EMAIL>',
            'senha': '2c2f4d175767c32fc4eab398a880c747',
            'uuidDispositivo': 'efd07455-cfa4-4021-bd84-6515a60f4d87'
        }

        response = requests.post(url, headers=headers, json=data)
        assert response.status_code == 200, f"Expected status code 200, but got {response.status_code}"

        login_data = response.json()
        self.uuidUsuario = login_data.get('uuid')

    @task
    def listar_posto(self):
        url = 'https://hml-api.postoaki.app/api/v3/posto/listar-page?page=0&size=10&listarPrecos=false&orderBy=location&uuidUsuario=63227205-c030-a7f9-8f37-c38b42e70552'
        headers = {
            'X-Client-Name': 'app',
            'X-Grupo-Empresa': '1a321181-41ff-2590-d4b9-28dd0a0956ba',
            'Authorization': f'Bearer {self.access_token}',
            'Cookie': 'JSESSIONID=B61078C9F04B2B88861629996A6D9AB3'
        }
        response = requests.get(url, headers=headers)

class WebsiteUser(HttpUser):
    tasks = [UserBehavior]
    wait_time = between(1, 5)
    host = "https://hml-api.postoaki.app"
