response='{"ultimoDeploy":"2025-01-29T01:54:34","horaBrasilia":"2025-01-29T01:36:27.688809228","artifactId":"@project.artifactId@","isEnviandoMensagens":true,"horaServidor":"2025-01-29T04:36:27.688814198","version":"@project.version@","status":"UP","contadorDeThreadsDeMensagens":1}'
isEnviando=$(echo $response | grep -o '"isEnviandoMensagens"\s*:\s*true')

echo "isEnviando: $isEnviando"

if [ -z "$isEnviando" ]; then
    echo "Pod $POD_NAME: Safe to terminate - no messages being sent $response"
    # log_to_loki "Pod $POD_NAME: Safe to terminate - no messages being sent"
    exit 0
else
    echo "Pod $POD_NAME: Safe to terminate - no messages being sent $response"
    # log_to_loki "Pod $POD_NAME: Messages still being sent, waiting..."
    sleep 5
fi