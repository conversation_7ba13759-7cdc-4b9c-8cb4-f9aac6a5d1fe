# Method 1: Using kubectl logs
kubectl logs -A --all-containers --tail=-1 2>&1 | grep "Safe to terminate"

# Method 2: Using stern (more readable output)
stern ".*" --all-namespaces --container ".*" | grep "Safe to terminate"

# Method 3: Using kubectl with context
for ns in $(kubectl get ns -o jsonpath='{.items[*].metadata.name}'); do
  for pod in $(kubectl get pods -n $ns -o jsonpath='{.items[*].metadata.name}'); do
    kubectl logs -n $ns $pod --all-containers | grep "Safe to terminate" | sed "s/^/$ns\/$pod: /"
  done
done

# Install stern if not present
brew install stern