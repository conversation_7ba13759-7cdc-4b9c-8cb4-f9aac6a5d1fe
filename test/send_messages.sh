#!/bin/bash

echo "Starting health check for api.postoaki.com..."

while true; do
    response=$(curl -sk https://api.postoaki.com/health)
    if [ $? -ne 0 ]; then
        echo "Failed to connect to health endpoint"
        sleep 1
        continue
    fi

    # Check if response contains isEnviandoMensagens
    if echo "$response" | grep -q '"isEnviandoMensagens":true'; then
        echo "Found isEnviandoMensagens:true"
        echo "Full response: $response"
        exit 0
    else
        sleep 1
    fi
done