const axios = require('axios');

// URL alvo
const targetUrl = 'https://scheduled.api.postoaki.com/health';
// Número de requisições
const requestCount = 50;
// Array para armazenar resultados
const results = [];

async function makeRequest(index) {
  try {
    console.log(`Fazendo requisição ${index + 1}/${requestCount}...`);
    const response = await axios.get(targetUrl);
    
    // Verificar se o atributo scheduled existe e é true
    const isScheduledTrue = response.data && response.data.scheduled === true;
    
    results.push({
      requestNumber: index + 1,
      status: response.status,
      scheduled: response.data?.scheduled,
      isScheduledTrue: isScheduledTrue
    });
    
    console.log(`Requisição ${index + 1}: status=${response.status}, scheduled=${response.data?.scheduled}`);
    
    return isScheduledTrue;
  } catch (error) {
    console.error(`Erro na requisição ${index + 1}: ${error.message}`);
    results.push({
      requestNumber: index + 1,
      error: error.message,
      isScheduledTrue: false
    });
    return false;
  }
}

async function runTest() {
  console.log(`Iniciando teste com ${requestCount} requisições para ${targetUrl}`);
  
  // Fazer as requisições em sequência
  for (let i = 0; i < requestCount; i++) {
    await makeRequest(i);
  }
  
  // Resumo dos resultados
  const successCount = results.filter(r => !r.error).length;
  const scheduledTrueCount = results.filter(r => r.isScheduledTrue).length;
  
  console.log('\n===== RESUMO DOS RESULTADOS =====');
  console.log(`Total de requisições: ${requestCount}`);
  console.log(`Requisições bem-sucedidas: ${successCount}`);
  console.log(`Requisições com scheduled=true: ${scheduledTrueCount}`);
  
  if (scheduledTrueCount === requestCount) {
    console.log('✅ SUCESSO: Todas as requisições retornaram scheduled=true');
  } else {
    console.log(`❌ FALHA: Apenas ${scheduledTrueCount}/${requestCount} requisições retornaram scheduled=true`);
  }
}

// Executar o teste
runTest();
