# Documentação de Teste

Este projeto contém uma suíte de testes de carga utilizando Locust e um script de migração de DNS para o Cloudflare.

## Testes de Carga com Locust

### Pré-requisitos

- Python 3.x
- Locust

### Instalação

1. Instale o Locust:

Navegue até a pasta de teste:

    ```sh
    cd test
    ```

   ```sh
   pip install locust
   ```

2. Executando os Testes

Navegue até a pasta do projeto onde o arquivo locustfile.py está localizado.
Execute o Locust:

```
locust
```

3. Abra o navegador e acesse http://localhost:8089 para iniciar a interface web do Locust.

4. Configure o número de usuários e a taxa de spawn, e clique em "Start Swarming" para iniciar os testes de carga.

## Script de Migração de DNS para Cloudflare

### Pré-requisitos

- Node.js
- npm ou yarn

### Instalação

Navegue até a pasta de teste:

```sh
cd test
```

2. Instale as dependências:

```
npm install
```

### Configuração

1. Defina os domínios que deseja migrar no arquivo dns.csv, alterando a coluna Migrando para Sim.

##### Executando o Script de Migração

Execute o script de migração:

```
node dns-migrate.js
```

Observações

* Certifique-se de que o arquivo dns.csv está corretamente formatado e contém as colunas necessárias.
* O script de migração atualizará os registros DNS no Cloudflare e verificará a resolução DNS e a saúde das URLs.
* Você pode precisar alterar o token cloudflare no script dns-migrate.js.
